import{i as V,l as E}from"./lodash-BwTBBKJP.js";import{_ as $,r as i,o as B,a as P,b as s,c as h,f as W,h as I,w as T,g as t,m as v,p as _,F as N,l as O,t as A,H as F,n as k,q as R,s as Z}from"./index-Dl7Mw3UB.js";import{a as q,b as U}from"./index-DfmmP2ML.js";const j={__name:"index",props:["data"],setup(e,{expose:n}){const y=i(),a=i([40,50]),f=e;let d;const b=[],x=["#5cc","#c5c","#5c5"];for(let o=0;o<24;o++)b.push({gt:o,lt:o+1,color:x[o%3]});let r={title:{text:"Alk1 电解槽"},xAxis:{type:"category",boundaryGap:!1},tooltip:{trigger:"axis"},legend:{data:["下发","实时"]},yAxis:{type:"value",boundaryGap:[0,"10%"]},visualMap:{type:"piecewise",show:!1,dimension:0,seriesIndex:0,pieces:b},series:[]};const g=()=>{d.resize()};return B(()=>{d=V(y.value),d.setOption({...r,...f.data});const o=E.throttle(()=>g(),300);window.addEventListener("resize",o),d.on("datazoom",u=>{var L;const D=((L=u==null?void 0:u.batch)==null?void 0:L[0])||u;a.value=[D.start,D.end]}),setTimeout(()=>{g()},2e3)}),P(()=>{d.clear(),r={...r,...f.data},d.clear(),r.dataZoom[0].start=a.value[0],r.dataZoom[0].end=a.value[1],d.setOption(r,!0)}),n({resize:g}),(o,u)=>(s(),h("div",{class:"line_chart",ref_key:"container",ref:y},null,512))}},p=$(j,[["__scopeId","data-v-3d3c91aa"]]),J={title:"",plan:[420,182,291,234,290,330,310,120,132,101,134,90,230,210,120,132,101,134,90,230,210,120,132,101],real:[230,210,120,132,101,134,90,230,410,120,132,101,134,90,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0],fix:[230,210,120,132,101,334,90,230,310,120,132,101,134,90,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0]},C=e=>{const n=[],y=["#888","#87d068"];for(let a=0;a<24;a++)n.push({gt:a,lt:a+1,color:y[e.plan[a]===0?0:1]});return{title:{text:e.title,textStyle:{color:"#55c"}},color:["#5cc","#1677ff"],tooltip:{trigger:"axis",formatter:function(a){return`
          <div style="display:flex;align-items:center;"><div style="width:10px;height:10px;background:#1677ff;margin-right:5px;border-radius:50%"></div>${a[1].seriesName} ${a[1].value}</div>
          <div style="display:flex;align-items:center;"><div style="width:10px;height:10px;background:#5cc;margin-right:5px;border-radius:50%"></div>${a[2].seriesName} ${a[2].value||"-"}</div>
        `}},legend:{data:["下发","实时"],top:"5%",right:"0",icon:"circle",itemHeight:10},grid:{top:"20%",bottom:"10%",left:"5%",right:"5%",containLabel:!0},yAxis:{type:"value",boundaryGap:[0,"10%"],name:"MW"},visualMap:{type:"piecewise",show:!1,dimension:0,seriesIndex:0,pieces:n},series:[{type:"line",smooth:.6,symbol:"none",lineStyle:{width:0},markLine:{},areaStyle:{},data:new Array(24).fill(20)},{name:"下发",type:"line",data:e.plan,symbolSize:0,lineStyle:{symbolSize:3,color:"#1677ff"}},{name:"实时",type:"line",data:e.real,symbolSize:0,lineStyle:{symbolSize:3,color:"#5cc"}}]}},Q=e=>({title:{text:e.title},grid:{top:"15%",bottom:"5%",left:"5%",right:"5%",containLabel:!0},legend:{data:["原始预测","预测修正","实时"],top:"0%",right:"0",icon:"circle",itemHeight:10},yAxis:{type:"value",boundaryGap:[0,"20%"],splitLine:{show:!1}},series:[{name:"原始预测",type:"line",stack:"Total",data:e.plan,symbolSize:0,lineStyle:{type:"dashed",symbolSize:3,color:"#1677ff",width:3}},{name:"预测修正",type:"line",stack:"Total",data:e.fix,symbolSize:0,lineStyle:{type:"dashed",showSymbol:!1}},{name:"实时",type:"line",stack:"Total",data:e.real,symbolSize:0,lineStyle:{type:"dashed",showSymbol:!1}}]}),K=e=>({title:{text:e.title,textStyle:{color:"#55c"}},yAxis:{type:"value",boundaryGap:[0,"20%"],name:"MW"},series:[{type:"line",data:e.plan,symbolSize:0,lineStyle:{color:"#5470C6"}}]}),z=e=>(R("data-v-fbde811b"),e=e(),Z(),e),X={class:"body_wrap"},Y=z(()=>t("div",{class:"title1"}," 策略状态 ",-1)),tt=z(()=>t("div",{class:"real_data_wrap"},[t("div",{class:"r_item"},[t("span",{class:"r_title"},"策略名称："),k("多槽联动寿命优先策略")]),t("div",{class:"r_item"},[t("span",{class:"r_title"},"状态："),k("运行中")]),t("div",{class:"r_item"},[t("span",{class:"r_title"},"执行时间："),k("2024.9.1-2025.9.2")]),t("div",{class:"r_item"},[t("span",{class:"r_title"},"循环次数："),k("91")])],-1)),et={class:"load_line_wrap"},at={class:"load_card"},st=z(()=>t("div",{class:"card_title"},"动态负荷功率",-1)),lt={class:"load_line_chart"},it={class:"load_card"},nt=z(()=>t("div",{class:"card_title"},"实时指标",-1)),ot={class:"load_line_chart c_right_wrap"},ct={class:"r_wrap"},dt={class:"r_title"},rt={class:"r_content"},_t={class:"chart_tab"},ut={class:"tab_box_wrap"},vt=["onClick"],pt={class:"t_left"},ht={class:"title"},yt={class:"value"},ft=z(()=>t("div",{class:"status_wrap"},[t("div",{class:"status_color"})],-1)),gt={key:0},mt={class:"chart_content_wrap"},wt={class:"chart_wrap single_chart_wrap"},bt={key:1},kt={class:"chart_content_wrap"},xt={class:"chart_wrap single_chart_wrap"},St={key:2},zt={class:"chart_content_wrap"},Dt={class:"chart_wrap single_chart_wrap"},Lt={key:3},At={class:"chart_content_wrap"},Ct={class:"chart_wrap single_chart_wrap"},Kt={key:4},Ht={class:"status_tag_wrap"},It={class:"tab_items"},Tt={class:"chart_content_wrap"},Wt={class:"chart_wrap"},Nt={class:"chart_wrap"},Ot={class:"chart_wrap"},$t={class:"chart_wrap"},Bt={__name:"index",setup(e){const n=i("alk"),y=i(),a=i(),f=i(),d=i(),b=i(),x=i(),r=i(),g=i(),o=i(),u=i(!1),D=[{title:"调度下达率",value:"99.95%"},{title:"功率损耗率",value:"0.75%"},{title:"风光消纳率",value:"81.08%"},{title:"储能SOH",value:"100%"},{title:"系统效率",value:"59.95%"}],L=[{title:"光伏(实时功率)",value:"5000KW",icon:"",key:"pv"},{title:"风电(实时功率)",value:"0KW",icon:"",key:"wind"},{title:"电网(实时功率)",value:"-1500KW",icon:"",key:"grid"},{title:"储能(实时功率)",value:"-500KW",icon:"",key:"bat"},{title:"制氢(实时功率)",value:"3000KW",icon:"",key:"alk"}],G=l=>{console.log("key:",l),n.value=l},c=(l,m)=>{var S;return(S=l==null?void 0:l.find(H=>H.dev_id===m))==null?void 0:S.data},M=async()=>{u.value=!0;const{data:l}=await q(),{data:m}=await U();u.value=!1,y.value=C({title:"ALKHi 01 电解槽",plan:c(l,1),real:c(m,1)}),a.value=C({title:"ALKHi 02 电解槽",plan:c(l,2),real:c(m,2)}),f.value=C({title:"ALKHi 03 电解槽",plan:c(l,3),real:c(m,3)}),d.value=C({title:"ALKHi 04 电解槽",plan:c(l,4),real:c(m,4)}),b.value=K({title:"光伏",plan:c(l,5)}),x.value=K({title:"风电",plan:c(l,6)}),r.value=K({title:"电网",plan:c(l,7)}),g.value=K({title:"储能",plan:c(l,8)})};return B(()=>{o.value=Q(J),M()}),(l,m)=>{const S=W("a-tag"),H=W("a-spin");return s(),h("div",X,[I(H,{spinning:u.value},{default:T(()=>[Y,tt,t("div",et,[t("div",at,[st,t("div",lt,[o.value?(s(),v(p,{key:0,class:"chart_content",data:o.value},null,8,["data"])):_("",!0)])]),t("div",it,[nt,t("div",ot,[(s(),h(N,null,O(D,w=>t("div",ct,[t("div",dt,A(w.title),1),t("div",rt,A(w.value),1)])),64))])])]),t("div",_t,[t("div",ut,[(s(),h(N,null,O(L,w=>t("div",{class:F(["t_card",{t_card_sel:n.value===w.key}]),onClick:Gt=>G(w.key)},[t("div",pt,[t("div",ht,A(w.title),1),t("div",yt,A(w.value),1)])],10,vt)),64))]),ft,t("div",null,[n.value==="pv"?(s(),h("div",gt,[t("div",mt,[t("div",wt,[b.value?(s(),v(p,{key:0,class:"chart_content",data:b.value},null,8,["data"])):_("",!0)])])])):n.value==="wind"?(s(),h("div",bt,[t("div",kt,[t("div",xt,[x.value?(s(),v(p,{key:0,class:"chart_content",data:x.value},null,8,["data"])):_("",!0)])])])):n.value==="grid"?(s(),h("div",St,[t("div",zt,[t("div",Dt,[r.value?(s(),v(p,{key:0,class:"chart_content",data:r.value},null,8,["data"])):_("",!0)])])])):n.value==="bat"?(s(),h("div",Lt,[t("div",At,[t("div",Ct,[g.value?(s(),v(p,{key:0,class:"chart_content",data:g.value},null,8,["data"])):_("",!0)])])])):n.value==="alk"?(s(),h("div",Kt,[t("div",Ht,[t("div",It,[I(S,{color:"#888"},{default:T(()=>[k("停机")]),_:1}),I(S,{color:"#87d068"},{default:T(()=>[k("运行")]),_:1})])]),t("div",Tt,[t("div",Wt,[y.value?(s(),v(p,{key:0,class:"chart_content",data:y.value},null,8,["data"])):_("",!0)]),t("div",Nt,[a.value?(s(),v(p,{key:0,class:"chart_content",data:a.value},null,8,["data"])):_("",!0)]),t("div",Ot,[f.value?(s(),v(p,{key:0,class:"chart_content",data:f.value},null,8,["data"])):_("",!0)]),t("div",$t,[d.value?(s(),v(p,{key:0,class:"chart_content",data:d.value},null,8,["data"])):_("",!0)])])])):_("",!0)])])]),_:1},8,["spinning"])])}}},Pt=$(Bt,[["__scopeId","data-v-fbde811b"]]);export{Pt as default};
