import{Q as v,r as g,d as b,z as h,f as o,b as x,c as y,g as d,h as e,w as t,n as C,R as I,C as S,q,s as B,_ as N}from"./index-Dl7Mw3UB.js";const V=n=>(q("data-v-27d45ec7"),n=n(),B(),n),k={class:"bg_wrap"},R={class:"login_window"},U=V(()=>d("div",{class:"title"},"综合能源规划设计系统",-1)),z=v({__name:"index",setup(n){const r=g(!1),_=b(),a=h({username:"",password:""}),i=async u=>{r.value=!0;const{code:s,msg:l}=await I(u);r.value=!1,s===0?(console.log("login success"),_.push({name:"home"})):S.error(l)};return(u,s)=>{const l=o("a-input"),p=o("a-form-item"),m=o("a-input-password"),f=o("a-button"),w=o("a-form");return x(),y("div",k,[d("div",R,[U,e(w,{class:"form_wrap",model:a,name:"basic","label-col":{span:6},"wrapper-col":{span:14},autocomplete:"off",onFinish:i},{default:t(()=>[e(p,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名!"}]},{default:t(()=>[e(l,{value:a.username,"onUpdate:value":s[0]||(s[0]=c=>a.username=c)},null,8,["value"])]),_:1}),e(p,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码!"}]},{default:t(()=>[e(m,{value:a.password,"onUpdate:value":s[1]||(s[1]=c=>a.password=c)},null,8,["value"])]),_:1}),e(p,{"wrapper-col":{offset:6,span:14}},{default:t(()=>[e(f,{loading:r.value,style:{width:"100%"},type:"primary","html-type":"submit"},{default:t(()=>[C(" 登录 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),F=N(z,[["__scopeId","data-v-27d45ec7"]]);export{F as default};
