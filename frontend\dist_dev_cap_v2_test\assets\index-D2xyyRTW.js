import{_ as w,r as p,e as C,f as u,b as I,c as h,h as t,w as r,F as x,l as j,q as D,s as A,g as f,d as P,y as F,o as R,k as $,n as K}from"./index-Dl7Mw3UB.js";import{b as E,a as B}from"./index-DollO33v.js";import{g as q,t as Y,a as N,y as V,b as G,c as H,P as O,K as U,S as z}from"./SensitivityAnalysis-Bg0ragqi.js";import{s as L,d as M}from"./index-DvkKnY0l.js";const J=d=>(D("data-v-7a9ab16f"),d=d(),A(),d),Q={class:"detail-indicators"},W=J(()=>f("h2",{class:"section-title"},"详情指标",-1)),X={__name:"TabTables",props:{resultData:{type:Object,default:()=>null}},setup(d){const i=d,n=p("2"),y=C(()=>{var e,s;return((s=(e=i.resultData)==null?void 0:e.financialIndicatorsSummary)==null?void 0:s.operatingYears)||25}),m=C(()=>y.value+1),b=[{key:"2",tab:"固定资产投资估算",type:"basic",dataKey:"fixedAssetsInvestmentEstimation",configKey:"fixedAssets"},{key:"3",tab:"工程总概算",type:"basic",dataKey:"projectOverallBudget",configKey:"projectBudget"},{key:"4",tab:"融资计划",type:"basic",dataKey:"financingPlan",configKey:"financing"},{key:"5",tab:"投资计划与资金筹措",type:"basic",dataKey:"investmentPlanAndFundRaising",configKey:"investmentPlan"},{key:"6",tab:"年度制氢及上网电量",type:"yearly",dataKey:"annualHydrogenAndGridPower",configKey:"annualHydrogenAndGridPower"},{key:"7",tab:"还本付息计算",type:"yearly",dataKey:"loanRepaymentSchedule",configKey:"loanRepaymentSchedule"},{key:"8",tab:"总成本费用",type:"yearly",dataKey:"totalCostAndExpenses",configKey:"totalCostAndExpenses"},{key:"9",tab:"利润和利润分配",type:"yearly",dataKey:"profitAndProfitDistribution",configKey:"profitAndProfitDistribution"},{key:"10",tab:"项目投资现金流量",type:"yearly",dataKey:"projectInvestmentCashFlow",configKey:"projectInvestmentCashFlow"},{key:"11",tab:"资本金财务现金流量",type:"yearly",dataKey:"equityCapitalCashFlow",configKey:"equityCapitalCashFlow"},{key:"12",tab:"财务计划现金流量",type:"yearly",dataKey:"financialPlanCashFlow",configKey:"financialPlanCashFlow"},{key:"13",tab:"资产负债",type:"yearly",dataKey:"balanceSheet",configKey:"balanceSheet"},{key:"14",tab:"财务指标汇总",type:"basic",dataKey:"financialIndicatorsSummary",configKey:"financialSummary"}],l=e=>e.type==="yearly"?q(m.value):Y[e.configKey],_=e=>{var s;return(s=i.resultData)!=null&&s[e.dataKey]?e.type==="yearly"?N(i.resultData[e.dataKey],V[e.configKey],m.value):G(i.resultData[e.dataKey],H[e.configKey]):[]};return(e,s)=>{const v=u("a-table"),g=u("a-tab-pane"),o=u("a-tabs");return I(),h("div",Q,[W,t(o,{activeKey:n.value,"onUpdate:activeKey":s[0]||(s[0]=a=>n.value=a),class:"detail-tabs"},{default:r(()=>[(I(),h(x,null,j(b,a=>t(g,{key:a.key,tab:a.tab},{default:r(()=>[t(v,{columns:l(a),"data-source":_(a),pagination:!1,size:"small",bordered:"",scroll:a.type==="yearly"?{x:"max-content"}:void 0},null,8,["columns","data-source","scroll"])]),_:2},1032,["tab"])),64))]),_:1},8,["activeKey"])])}}},Z=w(X,[["__scopeId","data-v-7a9ab16f"]]),aa={class:"economic-detail"},ea={class:"header-section"},ta={class:"title-wrapper"},oa={class:"header-actions"},na={__name:"index",setup(d){const i=P(),n=F(),y=p(null),m=p(null),b=p({}),l=p(!1),_=p("数据加载中..."),e=async()=>{try{l.value=!0;const{code:o,data:a,msg:c}=await E({projectId:n.params.projectId,solutionId:n.params.solutionId});console.log("接口返回数据:",a),console.log("lcoh:",a.resultTables.financialIndicatorsSummary.lcoh),console.log("Excel原 lcoh_ori:",a.resultTables.financialIndicatorsSummary.lcoh_ori),console.log("lcoe:",a.resultTables.financialIndicatorsSummary.lcoe),console.log("Excel原 lcoe_ori:",a.resultTables.financialIndicatorsSummary.lcoe_ori),console.log("敏感性分析数据:",a.resultAnalysis),o===0&&(y.value=a.resultTables,m.value=a.resultAnalysis||null)}catch(o){console.error("获取数据失败:",o),y.value=null}finally{l.value=!1}},s=async()=>{try{const{code:o,data:a,msg:c}=await B({projectId:n.params.projectId,solutionId:n.params.solutionId});o===0&&(b.value=a)}catch(o){console.error("获取方案信息失败:",o)}},v=()=>{i.push({path:"/report-detail",query:{projectId:n.params.projectId,solutionId:n.params.solutionId}})},g=async()=>{var a,c;l.value=!0,_.value="报告生成中...";const o=await L(`${location.origin}/report-detail?projectId=${n.params.projectId}&solutionId=${n.params.solutionId}&type=1`);await M(`/api/v1/cecp/result/export?encodedUrl=${o}`,`${(c=(a=b.value)==null?void 0:a.project)==null?void 0:c.name}.pdf`),setTimeout(()=>{l.value=!1},6500)};return R(async()=>{await Promise.all([e(),s()])}),(o,a)=>{const c=u("a-breadcrumb-item"),S=u("a-breadcrumb"),k=u("a-button"),T=u("a-spin");return I(),h("div",aa,[f("div",ea,[f("div",ta,[t(S,null,{default:r(()=>[t(c,null,{default:r(()=>[f("a",{href:"void:0",onClick:a[0]||(a[0]=sa=>$(i).back())}," < 经济分析")]),_:1}),t(c,null,{default:r(()=>[K("分析结果")]),_:1})]),_:1})]),f("div",oa,[t(k,{class:"action-btn",onClick:g,loading:l.value},{default:r(()=>[K("下载报告")]),_:1},8,["loading"]),t(k,{type:"primary",class:"action-btn",onClick:v},{default:r(()=>[K("查看报告")]),_:1})])]),t(T,{spinning:l.value,tip:_.value},{default:r(()=>[t(O,{"solution-info":b.value},null,8,["solution-info"]),t(U,{"result-data":y.value},null,8,["result-data"]),t(Z,{"result-data":y.value},null,8,["result-data"]),t(z,{"sensitivity-data":m.value},null,8,["sensitivity-data"])]),_:1},8,["spinning","tip"])])}}},ua=w(na,[["__scopeId","data-v-c6abf79f"]]);export{ua as default};
