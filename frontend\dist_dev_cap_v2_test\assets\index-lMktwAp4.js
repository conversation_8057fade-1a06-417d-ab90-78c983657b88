import{g as R}from"./index-DollO33v.js";import{o as U}from"./index-wGXFNFF9.js";import{g as V,f as W}from"./index-DvkKnY0l.js";import{p as q}from"./util-CvepLY_R.js";import{_ as X,M as A,d as Y,r as w,o as Z,f as N,b as l,c as _,g as r,h as z,w as v,k as i,C as b,n as d,F as x,t as m,p as c,m as I,l as ee,q as te,s as ae,S as oe,T as se}from"./index-Dl7Mw3UB.js";const le=[{title:"项目名称",dataIndex:"name",key:"name",width:150},{title:"项目背景",dataIndex:"desc",key:"desc",width:150,ellipsis:!0},{title:"客户名称",dataIndex:"customer",key:"customer",width:120},{title:"方案",key:"solutions",align:"center"}],ne=[{title:"方案名称",dataIndex:"name",key:"name",width:150},{title:"场景类型",dataIndex:"topology",key:"topology",width:255},{title:"项目收益率",dataIndex:"metrics",key:"metrics",width:130,align:"center"},{title:"提交时间",key:"createTime",width:160},{title:"操作",key:"action",width:150}],B={processing:{label:"计算中",color:"processing"},success:{label:"成功",color:"success"},failed:{label:"失败",color:"error"}},ie=o=>{var a;return((a=B[o])==null?void 0:a.color)||"default"},ce=o=>{var a;return((a=B[o])==null?void 0:a.label)||o},re=()=>["光伏","风机","电网","储能","制氢","储氢"],ue=o=>{const a=[],k=re();if(!o||!Array.isArray(o))return a;for(let g=0,t=k.length;g<t;g++)o[g]===1&&a.push({label:k[g]});return a},de=o=>(te("data-v-02524d9f"),o=o(),ae(),o),pe={class:"body_wrap"},_e={class:"p_wrap"},me={class:"content_wrap",id:"content_wrap"},ge={class:"part_wrap"},he={class:"p_title"},ye=de(()=>r("div",null,"经济分析",-1)),ve={class:"btn_wrap"},ke={class:"table_wrap"},fe={key:3,class:"t_btn_wrap"},we=["onClick"],be=["onClick"],Se=["onClick"],Ce={key:4},xe={__name:"index",setup(o){A.useModal();const a=Y(),k=w(!1),g=w([]),t=w([]),j=w(0);w(0);const h=w({pageSize:10,pageNumber:1}),F=e=>(h.value.pageNumber-1)*h.value.pageSize+e+1,O=()=>({selectedRowKeys:t.value,onSelect:$,hideSelectAll:!0,getCheckboxProps:e=>{const n=t.value.includes(e.id),s=t.value.length>=5;return{disabled:!n&&s,name:e.solutionName}}}),$=(e,n,s)=>{console.log("solution select:",e,n,s),n?(t.value.length>=5&&b.warning("最多只能选择5个方案"),t.value.includes(e.id)||(t.value.push(e.id),console.log("added solution:",e.id,"total selected:",t.value))):(t.value=t.value.filter(S=>S!==e.id),console.log("removed solution:",e.id,"total selected:",t.value))},E=(e,n,s)=>{console.log("Table change:",e,n,s),h.value.pageNumber=e.current,h.value.pageSize=e.pageSize,T()},T=async()=>{k.value=!0;try{const{code:e,data:{total:n,result:s}}=await R(h.value);g.value=s,j.value=n}catch(e){b.error("获取数据失败"),console.log("error",e)}finally{k.value=!1}},L=()=>{a.push({name:"economicAnalysisCreate"})},P=async e=>{console.log("delSolution",e),A.confirm({title:`确认删除当前方案 ${e.name}？`,icon:oe(se),async onOk(){const{code:n,msg:s}=await U([e.id]);n===0?(b.success("删除成功"),T()):b.error(s)},onCancel(){console.log("Cancel")}})},K=()=>{if(t.value.length<2){b.warning("请至少选择2个方案进行对比");return}if(t.value.length>5){b.warning("最多只能选择5个方案进行对比");return}a.push({path:"/economic/compare",query:{solutionIds:t.value.join(",")}})},J=e=>{console.log("查看:",e),a.push({name:"economicDetail",params:{projectId:e.projectId,solutionId:e.id}})},Q=e=>{console.log("修改:",e),a.push({name:"economicAnalysisCreate",query:{solutionId:e.id,projectId:e.projectId}})},G=()=>{T()};return Z(()=>{G()}),(e,n)=>{const s=N("a-button"),S=N("a-tag"),D=N("a-table");return l(),_("div",pe,[r("div",_e,[r("div",me,[r("div",ge,[r("div",he,[ye,r("div",ve,[z(s,{class:"btn_item",type:"primary",onClick:L},{default:v(()=>[d("新建")]),_:1}),z(s,{class:"btn_item",onClick:K},{default:v(()=>[d("方案对比")]),_:1})])]),r("div",ke,[z(D,{size:"small",bordered:"",class:"economic_table ant-table-striped","row-class-name":(f,C)=>C%2===1?"table-striped":null,loading:k.value,columns:i(le),"data-source":g.value,"row-key":"id",pagination:{pageSize:h.value.pageSize,current:h.value.pageNumber,total:j.value,hideOnSinglePage:!1,showTotal:(f,C)=>`共 ${f} 个项目`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"]},onChange:E},{bodyCell:v(({column:f,record:C,index:H})=>[f.key==="sequence"?(l(),_(x,{key:0},[d(m(F(H)),1)],64)):c("",!0),f.key==="solutionCount"?(l(),_(x,{key:1},[d(m(C.solutions.length),1)],64)):c("",!0),f.key==="solutions"?(l(),I(D,{key:2,size:"small",class:"inner_table",columns:i(ne),"data-source":C.solutions,pagination:!1,"row-selection":O(),"row-key":"id","show-header":!0},{bodyCell:v(({column:y,record:u})=>{var M;return[y.key==="status"?(l(),I(S,{key:0,color:i(ie)(u.status)},{default:v(()=>[d(m(i(ce)(u.status)),1)]),_:2},1032,["color"])):c("",!0),y.key==="profitRate"?(l(),_(x,{key:1},[d(m(u.profitRate)+"% ",1)],64)):c("",!0),y.key==="topology"?(l(!0),_(x,{key:2},ee(i(ue)(u.topology),p=>(l(),I(S,{key:p.label},{default:v(()=>[d(m(p.label),1)]),_:2},1024))),128)):c("",!0),y.key==="action"?(l(),_("div",fe,[r("a",{href:"javascript:void(0)",class:"a_item",onClick:p=>J(u)},"查看",8,we),r("a",{href:"javascript:void(0)",class:"a_item",onClick:p=>Q(u)},"修改",8,be),r("a",{href:"javascript:void(0)",class:"a_item",onClick:p=>P(u)},"删除",8,Se)])):c("",!0),y.key==="metrics"?(l(),_("div",Ce,m((u.metrics.projectInvestmentFIRR_afterTax*100).toFixed(2))+"%",1)):c("",!0),y.key==="status"?(l(),I(S,{key:5,color:(M=i(V)(i(q)(),e.text))==null?void 0:M.color},{default:v(()=>{var p;return[d(m((p=i(V)(i(q)(),e.text))==null?void 0:p.label),1)]}),_:1},8,["color"])):c("",!0),y.key==="createTime"?(l(),_(x,{key:6},[d(m(i(W)(u.createTime)),1)],64)):c("",!0)]}),_:2},1032,["columns","data-source","row-selection"])):c("",!0)]),_:1},8,["row-class-name","loading","columns","data-source","pagination"])])])])])])}}},De=X(xe,[["__scopeId","data-v-02524d9f"]]);export{De as default};
