import{u as o}from"./index-Dl7Mw3UB.js";const s=()=>{const{t}=o();return[{title:t("projectList.columns.projectName"),dataIndex:"name",width:"150px"},{title:t("projectList.columns.customerName"),dataIndex:"customer",width:"100px"},{title:t("projectList.columns.projectBackground"),dataIndex:"desc",width:"140px"},{title:t("projectList.columns.projectAction"),dataIndex:"action",key:"action",width:"150px"},{title:t("projectList.columns.solution"),dataIndex:"",key:"solution",width:"65%"}]},c=()=>{const{t}=o();return[{title:t("projectList.columns.solutionName"),dataIndex:"name",key:"name",width:"100px"},{title:t("projectList.columns.scenario"),dataIndex:"topology",key:"topology",width:"200px"},{title:t("projectList.columns.solveTarget"),dataIndex:"targetExpr",key:"targetExpr",width:"140px"},{title:t("projectList.columns.createTime"),dataIndex:"createTime",key:"createTime"},{title:t("projectList.columns.status"),dataIndex:"status",key:"status",width:"100px"},{title:t("common.action.action"),dataIndex:"action",key:"action",width:"330px"}]},r=()=>{const{t}=o();return[{label:t("projectList.status.running"),value:1,color:"processing"},{label:t("projectList.status.success"),value:2,color:"success"},{label:t("projectList.status.failed"),value:3,color:"error"}]},a=()=>{const{t}=o();return[t("projectList.targets.lcoh"),t("projectList.targets.lowestInvestment"),t("projectList.targets.lowestAbandonRate"),t("projectList.targets.maxHydrogenProduction")]},n=()=>{const{t}=o();return[t("projectList.topology.photovoltaic"),t("projectList.topology.windTurbine"),t("projectList.topology.grid"),t("projectList.topology.energyStorage"),t("projectList.topology.hydrogenProduction"),t("projectList.topology.hydrogenStorage")]};export{n as a,s as c,c as i,r as p,a as t};
