{"version": 3, "sources": ["../../ant-design-vue/es/vc-pagination/locale/en_US.js", "../../ant-design-vue/es/vc-picker/locale/en_US.js", "../../ant-design-vue/es/time-picker/locale/en_US.js", "../../ant-design-vue/es/date-picker/locale/en_US.js", "../../ant-design-vue/es/calendar/locale/en_US.js", "../../ant-design-vue/es/locale/en_US.js"], "sourcesContent": ["export default {\n  // Options.jsx\n  items_per_page: '/ page',\n  jump_to: 'Go to',\n  jump_to_confirm: 'confirm',\n  page: '',\n  // Pagination.jsx\n  prev_page: 'Previous Page',\n  next_page: 'Next Page',\n  prev_5: 'Previous 5 Pages',\n  next_5: 'Next 5 Pages',\n  prev_3: 'Previous 3 Pages',\n  next_3: 'Next 3 Pages'\n};", "const locale = {\n  locale: 'en_US',\n  today: 'Today',\n  now: 'Now',\n  backToToday: 'Back to today',\n  ok: 'Ok',\n  clear: 'Clear',\n  month: 'Month',\n  year: 'Year',\n  timeSelect: 'select time',\n  dateSelect: 'select date',\n  weekSelect: 'Choose a week',\n  monthSelect: 'Choose a month',\n  yearSelect: 'Choose a year',\n  decadeSelect: 'Choose a decade',\n  yearFormat: 'YYYY',\n  dateFormat: 'M/D/YYYY',\n  dayFormat: 'D',\n  dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n  monthBeforeYear: true,\n  previousMonth: 'Previous month (PageUp)',\n  nextMonth: 'Next month (PageDown)',\n  previousYear: 'Last year (Control + left)',\n  nextYear: 'Next year (Control + right)',\n  previousDecade: 'Last decade',\n  nextDecade: 'Next decade',\n  previousCentury: 'Last century',\n  nextCentury: 'Next century'\n};\nexport default locale;", "const locale = {\n  placeholder: 'Select time',\n  rangePlaceholder: ['Start time', 'End time']\n};\nexport default locale;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport CalendarLocale from '../../vc-picker/locale/en_US';\nimport TimePickerLocale from '../../time-picker/locale/en_US';\n// Merge into a locale object\nconst locale = {\n  lang: _extends({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, CalendarLocale),\n  timePickerLocale: _extends({}, TimePickerLocale)\n};\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nexport default locale;", "import enUS from '../../date-picker/locale/en_US';\nexport default enUS;", "/* eslint-disable no-template-curly-in-string */\nimport Pagination from '../vc-pagination/locale/en_US';\nimport Calendar from '../calendar/locale/en_US';\nimport DatePicker from '../date-picker/locale/en_US';\nimport TimePicker from '../time-picker/locale/en_US';\nconst typeTemplate = '${label} is not a valid ${type}';\nconst localeValues = {\n  locale: 'en',\n  Pagination,\n  DatePicker,\n  TimePicker,\n  Calendar,\n  global: {\n    placeholder: 'Please select'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    filterCheckall: 'Select all items',\n    filterSearchPlaceholder: 'Search in filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectNone: 'Clear all data',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting'\n  },\n  Tour: {\n    Next: 'Next',\n    Previous: 'Previous',\n    Finish: 'Finish'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand'\n  },\n  PageHeader: {\n    back: 'Back'\n  },\n  Form: {\n    optional: '(optional)',\n    defaultValidateMessages: {\n      default: 'Field validation error for ${label}',\n      required: 'Please enter ${label}',\n      enum: '${label} must be one of [${enum}]',\n      whitespace: '${label} cannot be a blank character',\n      date: {\n        format: '${label} date format is invalid',\n        parse: '${label} cannot be converted to a date',\n        invalid: '${label} is an invalid date'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label} must be ${len} characters',\n        min: '${label} must be at least ${min} characters',\n        max: '${label} must be up to ${max} characters',\n        range: '${label} must be between ${min}-${max} characters'\n      },\n      number: {\n        len: '${label} must be equal to ${len}',\n        min: '${label} must be minimum ${min}',\n        max: '${label} must be maximum ${max}',\n        range: '${label} must be between ${min}-${max}'\n      },\n      array: {\n        len: 'Must be ${len} ${label}',\n        min: 'At least ${min} ${label}',\n        max: 'At most ${max} ${label}',\n        range: 'The amount of ${label} must be between ${min}-${max}'\n      },\n      pattern: {\n        mismatch: '${label} does not match the pattern ${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: 'Preview'\n  },\n  QRCode: {\n    expired: 'QR code expired',\n    refresh: 'Refresh',\n    scanned: 'Scanned'\n  }\n};\nexport default localeValues;"], "mappings": ";;;;;AAAA,IAAO,gBAAQ;AAAA;AAAA,EAEb,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,MAAM;AAAA;AAAA,EAEN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;;;ACbA,IAAM,SAAS;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,aAAa;AAAA,EACb,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,aAAa;AACf;AACA,IAAOA,iBAAQ;;;AC7Bf,IAAMC,UAAS;AAAA,EACb,aAAa;AAAA,EACb,kBAAkB,CAAC,cAAc,UAAU;AAC7C;AACA,IAAOC,iBAAQD;;;ACAf,IAAME,UAAS;AAAA,EACb,MAAM,SAAS;AAAA,IACb,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,IAC/C,yBAAyB,CAAC,iBAAiB,aAAa;AAAA,IACxD,uBAAuB,CAAC,eAAe,WAAW;AAAA,IAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,EACjD,GAAGC,cAAc;AAAA,EACjB,kBAAkB,SAAS,CAAC,GAAGA,cAAgB;AACjD;AAGA,IAAOA,iBAAQD;;;ACpBf,IAAOE,iBAAQA;;;ACIf,IAAM,eAAe;AACrB,IAAM,eAAe;AAAA,EACnB,QAAQ;AAAA,EACR;AAAA,EACA,YAAAC;AAAA,EACA,YAAAA;AAAA,EACA,UAAAA;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,UAAU;AAAA,IACV,yBAAyB;AAAA,MACvB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AACA,IAAOA,iBAAQ;", "names": ["en_US_default", "locale", "en_US_default", "locale", "en_US_default", "en_US_default", "en_US_default"]}