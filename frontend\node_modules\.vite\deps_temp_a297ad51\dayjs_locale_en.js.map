{"version": 3, "sources": ["../../dayjs/locale/en.js"], "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n():\"function\"==typeof define&&define.amd?define(n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_en=n()}(this,(function(){\"use strict\";return{name:\"en\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),ordinal:function(e){var n=[\"th\",\"st\",\"nd\",\"rd\"],t=e%100;return\"[\"+e+(n[(t-20)%10]||n[t]||n[0])+\"]\"}}}));"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAM,EAAC,MAAK,MAAK,UAAS,2DAA2D,MAAM,GAAG,GAAE,QAAO,wFAAwF,MAAM,GAAG,GAAE,SAAQ,SAAS,GAAE;AAAC,YAAI,IAAE,CAAC,MAAK,MAAK,MAAK,IAAI,GAAE,IAAE,IAAE;AAAI,eAAM,MAAI,KAAG,GAAG,IAAE,MAAI,EAAE,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG;AAAA,MAAG,EAAC;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": []}