{"version": 3, "sources": ["../../decimal.js/decimal.mjs"], "sourcesContent": ["/*!\r\n *  decimal.js v10.5.0\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js\r\n *  Copyright (c) 2025 <PERSON> <<EMAIL>>\r\n *  MIT Licence\r\n */\r\n\r\n\r\n// -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\r\n\r\n\r\n  // The maximum exponent magnitude.\r\n  // The limit on the value of `toExpNeg`, `toExpPos`, `minE` and `maxE`.\r\nvar EXP_LIMIT = 9e15,                      // 0 to 9e15\r\n\r\n  // The limit on the value of `precision`, and on the value of the first argument to\r\n  // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n  MAX_DIGITS = 1e9,                        // 0 to 1e9\r\n\r\n  // Base conversion alphabet.\r\n  NUMERALS = '0123456789abcdef',\r\n\r\n  // The natural logarithm of 10 (1025 digits).\r\n  LN10 = '2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058',\r\n\r\n  // Pi (1025 digits).\r\n  PI = '3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789',\r\n\r\n\r\n  // The initial configuration properties of the Decimal constructor.\r\n  DEFAULTS = {\r\n\r\n    // These values must be integers within the stated ranges (inclusive).\r\n    // Most of these values can be changed at run-time using the `Decimal.config` method.\r\n\r\n    // The maximum number of significant digits of the result of a calculation or base conversion.\r\n    // E.g. `Decimal.config({ precision: 20 });`\r\n    precision: 20,                         // 1 to MAX_DIGITS\r\n\r\n    // The rounding mode used when rounding to `precision`.\r\n    //\r\n    // ROUND_UP         0 Away from zero.\r\n    // ROUND_DOWN       1 Towards zero.\r\n    // ROUND_CEIL       2 Towards +Infinity.\r\n    // ROUND_FLOOR      3 Towards -Infinity.\r\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    //\r\n    // E.g.\r\n    // `Decimal.rounding = 4;`\r\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\r\n    rounding: 4,                           // 0 to 8\r\n\r\n    // The modulo mode used when calculating the modulus: a mod n.\r\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n    // The remainder (r) is calculated as: r = a - n * q.\r\n    //\r\n    // UP         0 The remainder is positive if the dividend is negative, else is negative.\r\n    // DOWN       1 The remainder has the same sign as the dividend (JavaScript %).\r\n    // FLOOR      3 The remainder has the same sign as the divisor (Python %).\r\n    // HALF_EVEN  6 The IEEE 754 remainder function.\r\n    // EUCLID     9 Euclidian division. q = sign(n) * floor(a / abs(n)). Always positive.\r\n    //\r\n    // Truncated division (1), floored division (3), the IEEE 754 remainder (6), and Euclidian\r\n    // division (9) are commonly used for the modulus operation. The other rounding modes can also\r\n    // be used, but they may not give useful results.\r\n    modulo: 1,                             // 0 to 9\r\n\r\n    // The exponent value at and beneath which `toString` returns exponential notation.\r\n    // JavaScript numbers: -7\r\n    toExpNeg: -7,                          // 0 to -EXP_LIMIT\r\n\r\n    // The exponent value at and above which `toString` returns exponential notation.\r\n    // JavaScript numbers: 21\r\n    toExpPos:  21,                         // 0 to EXP_LIMIT\r\n\r\n    // The minimum exponent value, beneath which underflow to zero occurs.\r\n    // JavaScript numbers: -324  (5e-324)\r\n    minE: -EXP_LIMIT,                      // -1 to -EXP_LIMIT\r\n\r\n    // The maximum exponent value, above which overflow to Infinity occurs.\r\n    // JavaScript numbers: 308  (1.7976931348623157e+308)\r\n    maxE: EXP_LIMIT,                       // 1 to EXP_LIMIT\r\n\r\n    // Whether to use cryptographically-secure random number generation, if available.\r\n    crypto: false                          // true/false\r\n  },\r\n\r\n\r\n// ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\r\n\r\n\r\n  inexact, quadrant,\r\n  external = true,\r\n\r\n  decimalError = '[DecimalError] ',\r\n  invalidArgument = decimalError + 'Invalid argument: ',\r\n  precisionLimitExceeded = decimalError + 'Precision limit exceeded',\r\n  cryptoUnavailable = decimalError + 'crypto unavailable',\r\n  tag = '[object Decimal]',\r\n\r\n  mathfloor = Math.floor,\r\n  mathpow = Math.pow,\r\n\r\n  isBinary = /^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,\r\n  isHex = /^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,\r\n  isOctal = /^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,\r\n  isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\r\n\r\n  BASE = 1e7,\r\n  LOG_BASE = 7,\r\n  MAX_SAFE_INTEGER = 9007199254740991,\r\n\r\n  LN10_PRECISION = LN10.length - 1,\r\n  PI_PRECISION = PI.length - 1,\r\n\r\n  // Decimal.prototype object\r\n  P = { toStringTag: tag };\r\n\r\n\r\n// Decimal prototype methods\r\n\r\n\r\n/*\r\n *  absoluteValue             abs\r\n *  ceil\r\n *  clampedTo                 clamp\r\n *  comparedTo                cmp\r\n *  cosine                    cos\r\n *  cubeRoot                  cbrt\r\n *  decimalPlaces             dp\r\n *  dividedBy                 div\r\n *  dividedToIntegerBy        divToInt\r\n *  equals                    eq\r\n *  floor\r\n *  greaterThan               gt\r\n *  greaterThanOrEqualTo      gte\r\n *  hyperbolicCosine          cosh\r\n *  hyperbolicSine            sinh\r\n *  hyperbolicTangent         tanh\r\n *  inverseCosine             acos\r\n *  inverseHyperbolicCosine   acosh\r\n *  inverseHyperbolicSine     asinh\r\n *  inverseHyperbolicTangent  atanh\r\n *  inverseSine               asin\r\n *  inverseTangent            atan\r\n *  isFinite\r\n *  isInteger                 isInt\r\n *  isNaN\r\n *  isNegative                isNeg\r\n *  isPositive                isPos\r\n *  isZero\r\n *  lessThan                  lt\r\n *  lessThanOrEqualTo         lte\r\n *  logarithm                 log\r\n *  [maximum]                 [max]\r\n *  [minimum]                 [min]\r\n *  minus                     sub\r\n *  modulo                    mod\r\n *  naturalExponential        exp\r\n *  naturalLogarithm          ln\r\n *  negated                   neg\r\n *  plus                      add\r\n *  precision                 sd\r\n *  round\r\n *  sine                      sin\r\n *  squareRoot                sqrt\r\n *  tangent                   tan\r\n *  times                     mul\r\n *  toBinary\r\n *  toDecimalPlaces           toDP\r\n *  toExponential\r\n *  toFixed\r\n *  toFraction\r\n *  toHexadecimal             toHex\r\n *  toNearest\r\n *  toNumber\r\n *  toOctal\r\n *  toPower                   pow\r\n *  toPrecision\r\n *  toSignificantDigits       toSD\r\n *  toString\r\n *  truncated                 trunc\r\n *  valueOf                   toJSON\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */\r\nP.absoluteValue = P.abs = function () {\r\n  var x = new this.constructor(this);\r\n  if (x.s < 0) x.s = 1;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of positive Infinity.\r\n *\r\n */\r\nP.ceil = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal clamped to the range\r\n * delineated by `min` and `max`.\r\n *\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nP.clampedTo = P.clamp = function (min, max) {\r\n  var k,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n  min = new Ctor(min);\r\n  max = new Ctor(max);\r\n  if (!min.s || !max.s) return new Ctor(NaN);\r\n  if (min.gt(max)) throw Error(invalidArgument + max);\r\n  k = x.cmp(min);\r\n  return k < 0 ? min : x.cmp(max) > 0 ? max : new Ctor(x);\r\n};\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value,\r\n *   NaN  if the value of either Decimal is NaN.\r\n *\r\n */\r\nP.comparedTo = P.cmp = function (y) {\r\n  var i, j, xdL, ydL,\r\n    x = this,\r\n    xd = x.d,\r\n    yd = (y = new x.constructor(y)).d,\r\n    xs = x.s,\r\n    ys = y.s;\r\n\r\n  // Either NaN or ±Infinity?\r\n  if (!xd || !yd) {\r\n    return !xs || !ys ? NaN : xs !== ys ? xs : xd === yd ? 0 : !xd ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Either zero?\r\n  if (!xd[0] || !yd[0]) return xd[0] ? xs : yd[0] ? -ys : 0;\r\n\r\n  // Signs differ?\r\n  if (xs !== ys) return xs;\r\n\r\n  // Compare exponents.\r\n  if (x.e !== y.e) return x.e > y.e ^ xs < 0 ? 1 : -1;\r\n\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\r\n    if (xd[i] !== yd[i]) return xd[i] > yd[i] ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return xdL === ydL ? 0 : xdL > ydL ^ xs < 0 ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * cos(0)         = 1\r\n * cos(-0)        = 1\r\n * cos(Infinity)  = NaN\r\n * cos(-Infinity) = NaN\r\n * cos(NaN)       = NaN\r\n *\r\n */\r\nP.cosine = P.cos = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.d) return new Ctor(NaN);\r\n\r\n  // cos(0) = cos(-0) = 1\r\n  if (!x.d[0]) return new Ctor(1);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = cosine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 3 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *\r\n * Return a new Decimal whose value is the cube root of the value of this Decimal, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n *  cbrt(0)  =  0\r\n *  cbrt(-0) = -0\r\n *  cbrt(1)  =  1\r\n *  cbrt(-1) = -1\r\n *  cbrt(N)  =  N\r\n *  cbrt(-I) = -I\r\n *  cbrt(I)  =  I\r\n *\r\n * Math.cbrt(x) = (x < 0 ? -Math.pow(-x, 1/3) : Math.pow(x, 1/3))\r\n *\r\n */\r\nP.cubeRoot = P.cbrt = function () {\r\n  var e, m, n, r, rep, s, sd, t, t3, t3plusx,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = x.s * mathpow(x.s * x, 1 / 3);\r\n\r\n   // Math.cbrt underflow/overflow?\r\n   // Pass x to Math.pow as integer, then adjust the exponent of the result.\r\n  if (!s || Math.abs(s) == 1 / 0) {\r\n    n = digitsToString(x.d);\r\n    e = x.e;\r\n\r\n    // Adjust n exponent so it is a multiple of 3 away from x exponent.\r\n    if (s = (e - n.length + 1) % 3) n += (s == 1 || s == -2 ? '0' : '00');\r\n    s = mathpow(n, 1 / 3);\r\n\r\n    // Rarely, e may be one less than the result exponent value.\r\n    e = mathfloor((e + 1) / 3) - (e % 3 == (e < 0 ? -1 : 2));\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n    r.s = x.s;\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Halley's method.\r\n  // TODO? Compare Newton's method.\r\n  for (;;) {\r\n    t = r;\r\n    t3 = t.times(t).times(t);\r\n    t3plusx = t3.plus(x);\r\n    r = divide(t3plusx.plus(x).times(t), t3plusx.plus(t3), sd + 2, 1);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or 4999\r\n      // , i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */\r\nP.decimalPlaces = P.dp = function () {\r\n  var w,\r\n    d = this.d,\r\n    n = NaN;\r\n\r\n  if (d) {\r\n    w = d.length - 1;\r\n    n = (w - mathfloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    w = d[w];\r\n    if (w) for (; w % 10 == 0; w /= 10) n--;\r\n    if (n < 0) n = 0;\r\n  }\r\n\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n *  n / 0 = I\r\n *  n / N = N\r\n *  n / I = 0\r\n *  0 / n = 0\r\n *  0 / 0 = N\r\n *  0 / N = N\r\n *  0 / I = 0\r\n *  N / n = N\r\n *  N / 0 = N\r\n *  N / N = N\r\n *  N / I = N\r\n *  I / n = I\r\n *  I / 0 = I\r\n *  I / N = N\r\n *  I / I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedBy = P.div = function (y) {\r\n  return divide(this, new this.constructor(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedToIntegerBy = P.divToInt = function (y) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n  return finalise(divide(x, new Ctor(y), 0, 1, 1), Ctor.precision, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */\r\nP.equals = P.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of negative Infinity.\r\n *\r\n */\r\nP.floor = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 3);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */\r\nP.greaterThan = P.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */\r\nP.greaterThanOrEqualTo = P.gte = function (y) {\r\n  var k = this.cmp(y);\r\n  return k == 1 || k === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [1, Infinity]\r\n *\r\n * cosh(x) = 1 + x^2/2! + x^4/4! + x^6/6! + ...\r\n *\r\n * cosh(0)         = 1\r\n * cosh(-0)        = 1\r\n * cosh(Infinity)  = Infinity\r\n * cosh(-Infinity) = Infinity\r\n * cosh(NaN)       = NaN\r\n *\r\n *  x        time taken (ms)   result\r\n * 1000      9                 9.8503555700852349694e+433\r\n * 10000     25                4.4034091128314607936e+4342\r\n * 100000    171               1.4033316802130615897e+43429\r\n * 1000000   3817              1.5166076984010437725e+434294\r\n * 10000000  abandoned after 2 minute wait\r\n *\r\n * TODO? Compare performance of cosh(x) = 0.5 * (exp(x) + exp(-x))\r\n *\r\n */\r\nP.hyperbolicCosine = P.cosh = function () {\r\n  var k, n, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    one = new Ctor(1);\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s ? 1 / 0 : NaN);\r\n  if (x.isZero()) return one;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  // Argument reduction: cos(4x) = 1 - 8cos^2(x) + 8cos^4(x) + 1\r\n  // i.e. cos(x) = 1 - cos^2(x/4)(8 - 8cos^2(x/4))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  // TODO? Estimation reused from cosine() and may not be optimal here.\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    n = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    n = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(n), new Ctor(1), true);\r\n\r\n  // Reverse argument reduction\r\n  var cosh2_x,\r\n    i = k,\r\n    d8 = new Ctor(8);\r\n  for (; i--;) {\r\n    cosh2_x = x.times(x);\r\n    x = one.minus(cosh2_x.times(d8.minus(cosh2_x.times(d8))));\r\n  }\r\n\r\n  return finalise(x, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * sinh(x) = x + x^3/3! + x^5/5! + x^7/7! + ...\r\n *\r\n * sinh(0)         = 0\r\n * sinh(-0)        = -0\r\n * sinh(Infinity)  = Infinity\r\n * sinh(-Infinity) = -Infinity\r\n * sinh(NaN)       = NaN\r\n *\r\n * x        time taken (ms)\r\n * 10       2 ms\r\n * 100      5 ms\r\n * 1000     14 ms\r\n * 10000    82 ms\r\n * 100000   886 ms            1.4033316802130615897e+43429\r\n * 200000   2613 ms\r\n * 300000   5407 ms\r\n * 400000   8824 ms\r\n * 500000   13026 ms          8.7080643612718084129e+217146\r\n * 1000000  48543 ms\r\n *\r\n * TODO? Compare performance of sinh(x) = 0.5 * (exp(x) - exp(-x))\r\n *\r\n */\r\nP.hyperbolicSine = P.sinh = function () {\r\n  var k, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n  } else {\r\n\r\n    // Alternative argument reduction: sinh(3x) = sinh(x)(3 + 4sinh^2(x))\r\n    // i.e. sinh(x) = sinh(x/3)(3 + 4sinh^2(x/3))\r\n    // 3 multiplications and 1 addition\r\n\r\n    // Argument reduction: sinh(5x) = sinh(x)(5 + sinh^2(x)(20 + 16sinh^2(x)))\r\n    // i.e. sinh(x) = sinh(x/5)(5 + sinh^2(x/5)(20 + 16sinh^2(x/5)))\r\n    // 4 multiplications and 2 additions\r\n\r\n    // Estimate the optimum number of times to use the argument reduction.\r\n    k = 1.4 * Math.sqrt(len);\r\n    k = k > 16 ? 16 : k | 0;\r\n\r\n    x = x.times(1 / tinyPow(5, k));\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n\r\n    // Reverse argument reduction\r\n    var sinh2_x,\r\n      d5 = new Ctor(5),\r\n      d16 = new Ctor(16),\r\n      d20 = new Ctor(20);\r\n    for (; k--;) {\r\n      sinh2_x = x.times(x);\r\n      x = x.times(d5.plus(sinh2_x.times(d16.times(sinh2_x).plus(d20))));\r\n    }\r\n  }\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * tanh(x) = sinh(x) / cosh(x)\r\n *\r\n * tanh(0)         = 0\r\n * tanh(-0)        = -0\r\n * tanh(Infinity)  = 1\r\n * tanh(-Infinity) = -1\r\n * tanh(NaN)       = NaN\r\n *\r\n */\r\nP.hyperbolicTangent = P.tanh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 7;\r\n  Ctor.rounding = 1;\r\n\r\n  return divide(x.sinh(), x.cosh(), Ctor.precision = pr, Ctor.rounding = rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine (inverse cosine) in radians of the value of\r\n * this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [0, pi]\r\n *\r\n * acos(x) = pi/2 - asin(x)\r\n *\r\n * acos(0)       = pi/2\r\n * acos(-0)      = pi/2\r\n * acos(1)       = 0\r\n * acos(-1)      = pi\r\n * acos(1/2)     = pi/3\r\n * acos(-1/2)    = 2*pi/3\r\n * acos(|x| > 1) = NaN\r\n * acos(NaN)     = NaN\r\n *\r\n */\r\nP.inverseCosine = P.acos = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    k = x.abs().cmp(1),\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n    return k === 0\r\n      // |x| is 1\r\n      ? x.isNeg() ? getPi(Ctor, pr, rm) : new Ctor(0)\r\n      // |x| > 1 or x is NaN\r\n      : new Ctor(NaN);\r\n  }\r\n\r\n  if (x.isZero()) return getPi(Ctor, pr + 4, rm).times(0.5);\r\n\r\n  // TODO? Special case acos(0.5) = pi/3 and acos(-0.5) = 2*pi/3\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  // See https://github.com/MikeMcl/decimal.js/pull/217\r\n  x = new Ctor(1).minus(x).div(x.plus(1)).sqrt().atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [1, Infinity]\r\n * Range: [0, Infinity]\r\n *\r\n * acosh(x) = ln(x + sqrt(x^2 - 1))\r\n *\r\n * acosh(x < 1)     = NaN\r\n * acosh(NaN)       = NaN\r\n * acosh(Infinity)  = Infinity\r\n * acosh(-Infinity) = NaN\r\n * acosh(0)         = NaN\r\n * acosh(-0)        = NaN\r\n * acosh(1)         = 0\r\n * acosh(-1)        = NaN\r\n *\r\n */\r\nP.inverseHyperbolicCosine = P.acosh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.lte(1)) return new Ctor(x.eq(1) ? 0 : NaN);\r\n  if (!x.isFinite()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(Math.abs(x.e), x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).minus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * asinh(x) = ln(x + sqrt(x^2 + 1))\r\n *\r\n * asinh(NaN)       = NaN\r\n * asinh(Infinity)  = Infinity\r\n * asinh(-Infinity) = -Infinity\r\n * asinh(0)         = 0\r\n * asinh(-0)        = -0\r\n *\r\n */\r\nP.inverseHyperbolicSine = P.asinh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 2 * Math.max(Math.abs(x.e), x.sd()) + 6;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).plus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * atanh(x) = 0.5 * ln((1 + x) / (1 - x))\r\n *\r\n * atanh(|x| > 1)   = NaN\r\n * atanh(NaN)       = NaN\r\n * atanh(Infinity)  = NaN\r\n * atanh(-Infinity) = NaN\r\n * atanh(0)         = 0\r\n * atanh(-0)        = -0\r\n * atanh(1)         = Infinity\r\n * atanh(-1)        = -Infinity\r\n *\r\n */\r\nP.inverseHyperbolicTangent = P.atanh = function () {\r\n  var pr, rm, wpr, xsd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.e >= 0) return new Ctor(x.abs().eq(1) ? x.s / 0 : x.isZero() ? x : NaN);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  xsd = x.sd();\r\n\r\n  if (Math.max(xsd, pr) < 2 * -x.e - 1) return finalise(new Ctor(x), pr, rm, true);\r\n\r\n  Ctor.precision = wpr = xsd - x.e;\r\n\r\n  x = divide(x.plus(1), new Ctor(1).minus(x), wpr + pr, 1);\r\n\r\n  Ctor.precision = pr + 4;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.ln();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(0.5);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine (inverse sine) in radians of the value of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * asin(x) = 2*atan(x/(1 + sqrt(1 - x^2)))\r\n *\r\n * asin(0)       = 0\r\n * asin(-0)      = -0\r\n * asin(1/2)     = pi/6\r\n * asin(-1/2)    = -pi/6\r\n * asin(1)       = pi/2\r\n * asin(-1)      = -pi/2\r\n * asin(|x| > 1) = NaN\r\n * asin(NaN)     = NaN\r\n *\r\n * TODO? Compare performance of Taylor series.\r\n *\r\n */\r\nP.inverseSine = P.asin = function () {\r\n  var halfPi, k,\r\n    pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  k = x.abs().cmp(1);\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n\r\n    // |x| is 1\r\n    if (k === 0) {\r\n      halfPi = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      halfPi.s = x.s;\r\n      return halfPi;\r\n    }\r\n\r\n    // |x| > 1 or x is NaN\r\n    return new Ctor(NaN);\r\n  }\r\n\r\n  // TODO? Special case asin(1/2) = pi/6 and asin(-1/2) = -pi/6\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.div(new Ctor(1).minus(x.times(x)).sqrt().plus(1)).atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent (inverse tangent) in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n *\r\n * atan(0)         = 0\r\n * atan(-0)        = -0\r\n * atan(1)         = pi/4\r\n * atan(-1)        = -pi/4\r\n * atan(Infinity)  = pi/2\r\n * atan(-Infinity) = -pi/2\r\n * atan(NaN)       = NaN\r\n *\r\n */\r\nP.inverseTangent = P.atan = function () {\r\n  var i, j, k, n, px, t, r, wpr, x2,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (!x.isFinite()) {\r\n    if (!x.s) return new Ctor(NaN);\r\n    if (pr + 4 <= PI_PRECISION) {\r\n      r = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      r.s = x.s;\r\n      return r;\r\n    }\r\n  } else if (x.isZero()) {\r\n    return new Ctor(x);\r\n  } else if (x.abs().eq(1) && pr + 4 <= PI_PRECISION) {\r\n    r = getPi(Ctor, pr + 4, rm).times(0.25);\r\n    r.s = x.s;\r\n    return r;\r\n  }\r\n\r\n  Ctor.precision = wpr = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  // TODO? if (x >= 1 && pr <= PI_PRECISION) atan(x) = halfPi * x.s - atan(1 / x);\r\n\r\n  // Argument reduction\r\n  // Ensure |x| < 0.42\r\n  // atan(x) = 2 * atan(x / (1 + sqrt(1 + x^2)))\r\n\r\n  k = Math.min(28, wpr / LOG_BASE + 2 | 0);\r\n\r\n  for (i = k; i; --i) x = x.div(x.times(x).plus(1).sqrt().plus(1));\r\n\r\n  external = false;\r\n\r\n  j = Math.ceil(wpr / LOG_BASE);\r\n  n = 1;\r\n  x2 = x.times(x);\r\n  r = new Ctor(x);\r\n  px = x;\r\n\r\n  // atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n  for (; i !== -1;) {\r\n    px = px.times(x2);\r\n    t = r.minus(px.div(n += 2));\r\n\r\n    px = px.times(x2);\r\n    r = t.plus(px.div(n += 2));\r\n\r\n    if (r.d[j] !== void 0) for (i = j; r.d[i] === t.d[i] && i--;);\r\n  }\r\n\r\n  if (k) r = r.times(2 << (k - 1));\r\n\r\n  external = true;\r\n\r\n  return finalise(r, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is a finite number, otherwise return false.\r\n *\r\n */\r\nP.isFinite = function () {\r\n  return !!this.d;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */\r\nP.isInteger = P.isInt = function () {\r\n  return !!this.d && mathfloor(this.e / LOG_BASE) > this.d.length - 2;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is NaN, otherwise return false.\r\n *\r\n */\r\nP.isNaN = function () {\r\n  return !this.s;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */\r\nP.isNegative = P.isNeg = function () {\r\n  return this.s < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */\r\nP.isPositive = P.isPos = function () {\r\n  return this.s > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is 0 or -0, otherwise return false.\r\n *\r\n */\r\nP.isZero = function () {\r\n  return !!this.d && this.d[0] === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThan = P.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThanOrEqualTo = P.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * If no base is specified, return log[10](arg).\r\n *\r\n * log[base](arg) = ln(arg) / ln(base)\r\n *\r\n * The result will always be correctly rounded if the base of the log is 10, and 'almost always'\r\n * otherwise:\r\n *\r\n * Depending on the rounding mode, the result may be incorrectly rounded if the first fifteen\r\n * rounding digits are [49]99999999999999 or [50]00000000000000. In that case, the maximum error\r\n * between the result and the correctly rounded result will be one ulp (unit in the last place).\r\n *\r\n * log[-b](a)       = NaN\r\n * log[0](a)        = NaN\r\n * log[1](a)        = NaN\r\n * log[NaN](a)      = NaN\r\n * log[Infinity](a) = NaN\r\n * log[b](0)        = -Infinity\r\n * log[b](-0)       = -Infinity\r\n * log[b](-a)       = NaN\r\n * log[b](1)        = 0\r\n * log[b](Infinity) = Infinity\r\n * log[b](NaN)      = NaN\r\n *\r\n * [base] {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nP.logarithm = P.log = function (base) {\r\n  var isBase10, d, denominator, k, inf, num, sd, r,\r\n    arg = this,\r\n    Ctor = arg.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding,\r\n    guard = 5;\r\n\r\n  // Default base is 10.\r\n  if (base == null) {\r\n    base = new Ctor(10);\r\n    isBase10 = true;\r\n  } else {\r\n    base = new Ctor(base);\r\n    d = base.d;\r\n\r\n    // Return NaN if base is negative, or non-finite, or is 0 or 1.\r\n    if (base.s < 0 || !d || !d[0] || base.eq(1)) return new Ctor(NaN);\r\n\r\n    isBase10 = base.eq(10);\r\n  }\r\n\r\n  d = arg.d;\r\n\r\n  // Is arg negative, non-finite, 0 or 1?\r\n  if (arg.s < 0 || !d || !d[0] || arg.eq(1)) {\r\n    return new Ctor(d && !d[0] ? -1 / 0 : arg.s != 1 ? NaN : d ? 0 : 1 / 0);\r\n  }\r\n\r\n  // The result will have a non-terminating decimal expansion if base is 10 and arg is not an\r\n  // integer power of 10.\r\n  if (isBase10) {\r\n    if (d.length > 1) {\r\n      inf = true;\r\n    } else {\r\n      for (k = d[0]; k % 10 === 0;) k /= 10;\r\n      inf = k !== 1;\r\n    }\r\n  }\r\n\r\n  external = false;\r\n  sd = pr + guard;\r\n  num = naturalLogarithm(arg, sd);\r\n  denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n\r\n  // The result will have 5 rounding digits.\r\n  r = divide(num, denominator, sd, 1);\r\n\r\n  // If at a rounding boundary, i.e. the result's rounding digits are [49]9999 or [50]0000,\r\n  // calculate 10 further digits.\r\n  //\r\n  // If the result is known to have an infinite decimal expansion, repeat this until it is clear\r\n  // that the result is above or below the boundary. Otherwise, if after calculating the 10\r\n  // further digits, the last 14 are nines, round up and assume the result is exact.\r\n  // Also assume the result is exact if the last 14 are zero.\r\n  //\r\n  // Example of a result that will be incorrectly rounded:\r\n  // log[1048576](4503599627370502) = 2.60000000000000009610279511444746...\r\n  // The above result correctly rounded using ROUND_CEIL to 1 decimal place should be 2.7, but it\r\n  // will be given as 2.6 as there are 15 zeros immediately after the requested decimal place, so\r\n  // the exact result would be assumed to be 2.6, which rounded using ROUND_CEIL to 1 decimal\r\n  // place is still 2.6.\r\n  if (checkRoundingDigits(r.d, k = pr, rm)) {\r\n\r\n    do {\r\n      sd += 10;\r\n      num = naturalLogarithm(arg, sd);\r\n      denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n      r = divide(num, denominator, sd, 1);\r\n\r\n      if (!inf) {\r\n\r\n        // Check for 14 nines from the 2nd rounding digit, as the first may be 4.\r\n        if (+digitsToString(r.d).slice(k + 1, k + 15) + 1 == 1e14) {\r\n          r = finalise(r, pr + 1, 0);\r\n        }\r\n\r\n        break;\r\n      }\r\n    } while (checkRoundingDigits(r.d, k += 10, rm));\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.max = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, -1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.min = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, 1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n *  n - 0 = n\r\n *  n - N = N\r\n *  n - I = -I\r\n *  0 - n = -n\r\n *  0 - 0 = 0\r\n *  0 - N = N\r\n *  0 - I = -I\r\n *  N - n = N\r\n *  N - 0 = N\r\n *  N - N = N\r\n *  N - I = N\r\n *  I - n = I\r\n *  I - 0 = I\r\n *  I - N = N\r\n *  I - I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var d, e, i, j, k, len, pr, rm, xd, xe, xLTy, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return y negated if x is finite and y is ±Infinity.\r\n    else if (x.d) y.s = -y.s;\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with different signs.\r\n    // Return NaN if both are ±Infinity with the same sign.\r\n    else y = new Ctor(y.d || x.s !== y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n  // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.plus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return y negated if x is zero and y is non-zero.\r\n    if (yd[0]) y.s = -y.s;\r\n\r\n    // Return x if y is zero and x is non-zero.\r\n    else if (xd[0]) y = new Ctor(x);\r\n\r\n    // Return zero if both are zero.\r\n    // From IEEE 754 (2008) 6.3: 0 - 0 = -0 - -0 = -0 when rounding to -Infinity.\r\n    else return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  e = mathfloor(y.e / LOG_BASE);\r\n  xe = mathfloor(x.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  k = xe - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (k) {\r\n    xLTy = k < 0;\r\n\r\n    if (xLTy) {\r\n      d = xd;\r\n      k = -k;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = xe;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Numbers with massively different exponents would result in a very high number of\r\n    // zeros needing to be prepended, but this can be avoided while still ensuring correct\r\n    // rounding by limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\r\n    i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\r\n\r\n    if (k > i) {\r\n      k = i;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents.\r\n    d.reverse();\r\n    for (i = k; i--;) d.push(0);\r\n    d.reverse();\r\n\r\n  // Base 1e7 exponents equal.\r\n  } else {\r\n\r\n    // Check digits to determine which is the bigger number.\r\n\r\n    i = xd.length;\r\n    len = yd.length;\r\n    xLTy = i < len;\r\n    if (xLTy) len = i;\r\n\r\n    for (i = 0; i < len; i++) {\r\n      if (xd[i] != yd[i]) {\r\n        xLTy = xd[i] < yd[i];\r\n        break;\r\n      }\r\n    }\r\n\r\n    k = 0;\r\n  }\r\n\r\n  if (xLTy) {\r\n    d = xd;\r\n    xd = yd;\r\n    yd = d;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  len = xd.length;\r\n\r\n  // Append zeros to `xd` if shorter.\r\n  // Don't add zeros to `yd` if shorter as subtraction only needs to start at `yd` length.\r\n  for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\r\n\r\n  // Subtract yd from xd.\r\n  for (i = yd.length; i > k;) {\r\n\r\n    if (xd[--i] < yd[i]) {\r\n      for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\r\n      --xd[j];\r\n      xd[i] += BASE;\r\n    }\r\n\r\n    xd[i] -= yd[i];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xd[--len] === 0;) xd.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xd[0] === 0; xd.shift()) --e;\r\n\r\n  // Zero?\r\n  if (!xd[0]) return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n *   n % 0 =  N\r\n *   n % N =  N\r\n *   n % I =  n\r\n *   0 % n =  0\r\n *  -0 % n = -0\r\n *   0 % 0 =  N\r\n *   0 % N =  N\r\n *   0 % I =  0\r\n *   N % n =  N\r\n *   N % 0 =  N\r\n *   N % N =  N\r\n *   N % I =  N\r\n *   I % n =  N\r\n *   I % 0 =  N\r\n *   I % N =  N\r\n *   I % I =  N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * The result depends on the modulo mode.\r\n *\r\n */\r\nP.modulo = P.mod = function (y) {\r\n  var q,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // Return NaN if x is ±Infinity or NaN, or y is NaN or ±0.\r\n  if (!x.d || !y.s || y.d && !y.d[0]) return new Ctor(NaN);\r\n\r\n  // Return x if y is ±Infinity or x is ±0.\r\n  if (!y.d || x.d && !x.d[0]) {\r\n    return finalise(new Ctor(x), Ctor.precision, Ctor.rounding);\r\n  }\r\n\r\n  // Prevent rounding of intermediate calculations.\r\n  external = false;\r\n\r\n  if (Ctor.modulo == 9) {\r\n\r\n    // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n    // result = x - q * y    where  0 <= result < abs(y)\r\n    q = divide(x, y.abs(), 0, 3, 1);\r\n    q.s *= y.s;\r\n  } else {\r\n    q = divide(x, y, 0, Ctor.modulo, 1);\r\n  }\r\n\r\n  q = q.times(y);\r\n\r\n  external = true;\r\n\r\n  return x.minus(q);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalExponential = P.exp = function () {\r\n  return naturalExponential(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalLogarithm = P.ln = function () {\r\n  return naturalLogarithm(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */\r\nP.negated = P.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n *  n + 0 = n\r\n *  n + N = N\r\n *  n + I = I\r\n *  0 + n = n\r\n *  0 + 0 = 0\r\n *  0 + N = N\r\n *  0 + I = I\r\n *  N + n = N\r\n *  N + 0 = N\r\n *  N + N = N\r\n *  N + I = N\r\n *  I + n = I\r\n *  I + 0 = I\r\n *  I + N = N\r\n *  I + I = I\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.plus = P.add = function (y) {\r\n  var carry, d, e, i, k, len, pr, rm, xd, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with the same sign.\r\n    // Return NaN if both are ±Infinity with different signs.\r\n    // Return y if x is finite and y is ±Infinity.\r\n    else if (!x.d) y = new Ctor(y.d || x.s === y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n   // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return x if y is zero.\r\n    // Return y if y is non-zero.\r\n    if (!yd[0]) y = new Ctor(x);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  k = mathfloor(x.e / LOG_BASE);\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  i = k - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (i) {\r\n\r\n    if (i < 0) {\r\n      d = xd;\r\n      i = -i;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = k;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\r\n    k = Math.ceil(pr / LOG_BASE);\r\n    len = k > len ? k + 1 : len + 1;\r\n\r\n    if (i > len) {\r\n      i = len;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\r\n    d.reverse();\r\n    for (; i--;) d.push(0);\r\n    d.reverse();\r\n  }\r\n\r\n  len = xd.length;\r\n  i = yd.length;\r\n\r\n  // If yd is longer than xd, swap xd and yd so xd points to the longer array.\r\n  if (len - i < 0) {\r\n    i = len;\r\n    d = yd;\r\n    yd = xd;\r\n    xd = d;\r\n  }\r\n\r\n  // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\r\n  for (carry = 0; i;) {\r\n    carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\r\n    xd[i] %= BASE;\r\n  }\r\n\r\n  if (carry) {\r\n    xd.unshift(carry);\r\n    ++e;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n  for (len = xd.length; xd[--len] == 0;) xd.pop();\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */\r\nP.precision = P.sd = function (z) {\r\n  var k,\r\n    x = this;\r\n\r\n  if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\r\n\r\n  if (x.d) {\r\n    k = getPrecision(x.d);\r\n    if (z && x.e + 1 > k) k = x.e + 1;\r\n  } else {\r\n    k = NaN;\r\n  }\r\n\r\n  return k;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */\r\nP.round = function () {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  return finalise(new Ctor(x), x.e + 1, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n *\r\n * sin(0)         = 0\r\n * sin(-0)        = -0\r\n * sin(Infinity)  = NaN\r\n * sin(-Infinity) = NaN\r\n * sin(NaN)       = NaN\r\n *\r\n */\r\nP.sine = P.sin = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = sine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant > 2 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n *  sqrt(-n) =  N\r\n *  sqrt(N)  =  N\r\n *  sqrt(-I) =  N\r\n *  sqrt(I)  =  I\r\n *  sqrt(0)  =  0\r\n *  sqrt(-0) = -0\r\n *\r\n */\r\nP.squareRoot = P.sqrt = function () {\r\n  var m, n, sd, r, rep, t,\r\n    x = this,\r\n    d = x.d,\r\n    e = x.e,\r\n    s = x.s,\r\n    Ctor = x.constructor;\r\n\r\n  // Negative/NaN/Infinity/zero?\r\n  if (s !== 1 || !d || !d[0]) {\r\n    return new Ctor(!s || s < 0 && (!d || d[0]) ? NaN : d ? x : 1 / 0);\r\n  }\r\n\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = Math.sqrt(+x);\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n  if (s == 0 || s == 1 / 0) {\r\n    n = digitsToString(d);\r\n\r\n    if ((n.length + e) % 2 == 0) n += '0';\r\n    s = Math.sqrt(n);\r\n    e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Newton-Raphson iteration.\r\n  for (;;) {\r\n    t = r;\r\n    r = t.plus(divide(x, t, sd + 2, 1)).times(0.5);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\r\n      // 4999, i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * tan(0)         = 0\r\n * tan(-0)        = -0\r\n * tan(Infinity)  = NaN\r\n * tan(-Infinity) = NaN\r\n * tan(NaN)       = NaN\r\n *\r\n */\r\nP.tangent = P.tan = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.sin();\r\n  x.s = 1;\r\n  x = divide(x, new Ctor(1).minus(x.times(x)).sqrt(), pr + 10, 0);\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 4 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *  n * 0 = 0\r\n *  n * N = N\r\n *  n * I = I\r\n *  0 * n = 0\r\n *  0 * 0 = 0\r\n *  0 * N = N\r\n *  0 * I = N\r\n *  N * n = N\r\n *  N * 0 = N\r\n *  N * N = N\r\n *  N * I = N\r\n *  I * n = I\r\n *  I * 0 = N\r\n *  I * N = N\r\n *  I * I = I\r\n *\r\n * Return a new Decimal whose value is this Decimal times `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.times = P.mul = function (y) {\r\n  var carry, e, i, k, r, rL, t, xdL, ydL,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    xd = x.d,\r\n    yd = (y = new Ctor(y)).d;\r\n\r\n  y.s *= x.s;\r\n\r\n   // If either is NaN, ±Infinity or ±0...\r\n  if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n    return new Ctor(!y.s || xd && !xd[0] && !yd || yd && !yd[0] && !xd\r\n\r\n      // Return NaN if either is NaN.\r\n      // Return NaN if x is ±0 and y is ±Infinity, or y is ±0 and x is ±Infinity.\r\n      ? NaN\r\n\r\n      // Return ±Infinity if either is ±Infinity.\r\n      // Return ±0 if either is ±0.\r\n      : !xd || !yd ? y.s / 0 : y.s * 0);\r\n  }\r\n\r\n  e = mathfloor(x.e / LOG_BASE) + mathfloor(y.e / LOG_BASE);\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Ensure xd points to the longer array.\r\n  if (xdL < ydL) {\r\n    r = xd;\r\n    xd = yd;\r\n    yd = r;\r\n    rL = xdL;\r\n    xdL = ydL;\r\n    ydL = rL;\r\n  }\r\n\r\n  // Initialise the result array with zeros.\r\n  r = [];\r\n  rL = xdL + ydL;\r\n  for (i = rL; i--;) r.push(0);\r\n\r\n  // Multiply!\r\n  for (i = ydL; --i >= 0;) {\r\n    carry = 0;\r\n    for (k = xdL + i; k > i;) {\r\n      t = r[k] + yd[i] * xd[k - i - 1] + carry;\r\n      r[k--] = t % BASE | 0;\r\n      carry = t / BASE | 0;\r\n    }\r\n\r\n    r[k] = (r[k] + carry) % BASE | 0;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; !r[--rL];) r.pop();\r\n\r\n  if (carry) ++e;\r\n  else r.shift();\r\n\r\n  y.d = r;\r\n  y.e = getBase10Exponent(r, e);\r\n\r\n  return external ? finalise(y, Ctor.precision, Ctor.rounding) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 2, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toBinary = function (sd, rm) {\r\n  return toStringBinary(this, 2, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toDecimalPlaces = P.toDP = function (dp, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n  if (dp === void 0) return x;\r\n\r\n  checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n  if (rm === void 0) rm = Ctor.rounding;\r\n  else checkInt32(rm, 0, 8);\r\n\r\n  return finalise(x, dp + x.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x, true);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), dp + 1, rm);\r\n    str = finiteToString(x, true, dp + 1);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var str, y,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    y = finalise(new Ctor(x), dp + x.e + 1, rm);\r\n    str = finiteToString(y, false, dp + y.e + 1);\r\n  }\r\n\r\n  // To determine whether to add the minus sign look at the value before it was rounded,\r\n  // i.e. look at `x` rather than `y`.\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return an array representing the value of this Decimal as a simple fraction with an integer\r\n * numerator and an integer denominator.\r\n *\r\n * The denominator will be a positive non-zero value less than or equal to the specified maximum\r\n * denominator. If a maximum denominator is not specified, the denominator will be the lowest\r\n * value necessary to represent the number exactly.\r\n *\r\n * [maxD] {number|string|bigint|Decimal} Maximum denominator. Integer >= 1 and < Infinity.\r\n *\r\n */\r\nP.toFraction = function (maxD) {\r\n  var d, d0, d1, d2, e, k, n, n0, n1, pr, q, r,\r\n    x = this,\r\n    xd = x.d,\r\n    Ctor = x.constructor;\r\n\r\n  if (!xd) return new Ctor(x);\r\n\r\n  n1 = d0 = new Ctor(1);\r\n  d1 = n0 = new Ctor(0);\r\n\r\n  d = new Ctor(d1);\r\n  e = d.e = getPrecision(xd) - x.e - 1;\r\n  k = e % LOG_BASE;\r\n  d.d[0] = mathpow(10, k < 0 ? LOG_BASE + k : k);\r\n\r\n  if (maxD == null) {\r\n\r\n    // d is 10**e, the minimum max-denominator needed.\r\n    maxD = e > 0 ? d : n1;\r\n  } else {\r\n    n = new Ctor(maxD);\r\n    if (!n.isInt() || n.lt(n1)) throw Error(invalidArgument + n);\r\n    maxD = n.gt(d) ? (e > 0 ? d : n1) : n;\r\n  }\r\n\r\n  external = false;\r\n  n = new Ctor(digitsToString(xd));\r\n  pr = Ctor.precision;\r\n  Ctor.precision = e = xd.length * LOG_BASE * 2;\r\n\r\n  for (;;)  {\r\n    q = divide(n, d, 0, 1, 1);\r\n    d2 = d0.plus(q.times(d1));\r\n    if (d2.cmp(maxD) == 1) break;\r\n    d0 = d1;\r\n    d1 = d2;\r\n    d2 = n1;\r\n    n1 = n0.plus(q.times(d2));\r\n    n0 = d2;\r\n    d2 = d;\r\n    d = n.minus(q.times(d2));\r\n    n = d2;\r\n  }\r\n\r\n  d2 = divide(maxD.minus(d0), d1, 0, 1, 1);\r\n  n0 = n0.plus(d2.times(n1));\r\n  d0 = d0.plus(d2.times(d1));\r\n  n0.s = n1.s = x.s;\r\n\r\n  // Determine which fraction is closer to x, n0/d0 or n1/d1?\r\n  r = divide(n1, d1, e, 1).minus(x).abs().cmp(divide(n0, d0, e, 1).minus(x).abs()) < 1\r\n      ? [n1, d1] : [n0, d0];\r\n\r\n  Ctor.precision = pr;\r\n  external = true;\r\n\r\n  return r;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 16, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toHexadecimal = P.toHex = function (sd, rm) {\r\n  return toStringBinary(this, 16, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Returns a new Decimal whose value is the nearest multiple of `y` in the direction of rounding\r\n * mode `rm`, or `Decimal.rounding` if `rm` is omitted, to the value of this Decimal.\r\n *\r\n * The return value will always have the same sign as this Decimal, unless either this Decimal\r\n * or `y` is NaN, in which case the return value will be also be NaN.\r\n *\r\n * The return value is not affected by the value of `precision`.\r\n *\r\n * y {number|string|bigint|Decimal} The magnitude to round to a multiple of.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toNearest() rounding mode not an integer: {rm}'\r\n * 'toNearest() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toNearest = function (y, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (y == null) {\r\n\r\n    // If x is not finite, return x.\r\n    if (!x.d) return x;\r\n\r\n    y = new Ctor(1);\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    y = new Ctor(y);\r\n    if (rm === void 0) {\r\n      rm = Ctor.rounding;\r\n    } else {\r\n      checkInt32(rm, 0, 8);\r\n    }\r\n\r\n    // If x is not finite, return x if y is not NaN, else NaN.\r\n    if (!x.d) return y.s ? x : y;\r\n\r\n    // If y is not finite, return Infinity with the sign of x if y is Infinity, else NaN.\r\n    if (!y.d) {\r\n      if (y.s) y.s = x.s;\r\n      return y;\r\n    }\r\n  }\r\n\r\n  // If y is not zero, calculate the nearest multiple of y to x.\r\n  if (y.d[0]) {\r\n    external = false;\r\n    x = divide(x, y, 0, rm, 1).times(y);\r\n    external = true;\r\n    finalise(x);\r\n\r\n  // If y is zero, return zero with the sign of x.\r\n  } else {\r\n    y.s = x.s;\r\n    x = y;\r\n  }\r\n\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n * Zero keeps its sign.\r\n *\r\n */\r\nP.toNumber = function () {\r\n  return +this;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 8, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toOctal = function (sd, rm) {\r\n  return toStringBinary(this, 8, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`, rounded\r\n * to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * ECMAScript compliant.\r\n *\r\n *   pow(x, NaN)                           = NaN\r\n *   pow(x, ±0)                            = 1\r\n\r\n *   pow(NaN, non-zero)                    = NaN\r\n *   pow(abs(x) > 1, +Infinity)            = +Infinity\r\n *   pow(abs(x) > 1, -Infinity)            = +0\r\n *   pow(abs(x) == 1, ±Infinity)           = NaN\r\n *   pow(abs(x) < 1, +Infinity)            = +0\r\n *   pow(abs(x) < 1, -Infinity)            = +Infinity\r\n *   pow(+Infinity, y > 0)                 = +Infinity\r\n *   pow(+Infinity, y < 0)                 = +0\r\n *   pow(-Infinity, odd integer > 0)       = -Infinity\r\n *   pow(-Infinity, even integer > 0)      = +Infinity\r\n *   pow(-Infinity, odd integer < 0)       = -0\r\n *   pow(-Infinity, even integer < 0)      = +0\r\n *   pow(+0, y > 0)                        = +0\r\n *   pow(+0, y < 0)                        = +Infinity\r\n *   pow(-0, odd integer > 0)              = -0\r\n *   pow(-0, even integer > 0)             = +0\r\n *   pow(-0, odd integer < 0)              = -Infinity\r\n *   pow(-0, even integer < 0)             = +Infinity\r\n *   pow(finite x < 0, finite non-integer) = NaN\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * Assuming the first 15 rounding digits are each equally likely to be any digit 0-9, the\r\n * probability of an incorrectly rounded result\r\n * P([49]9{14} | [50]0{14}) = 2 * 0.2 * 10^-14 = 4e-15 = 1/2.5e+14\r\n * i.e. 1 in 250,000,000,000,000\r\n *\r\n * If a result is incorrectly rounded the maximum error will be 1 ulp (unit in last place).\r\n *\r\n * y {number|string|bigint|Decimal} The power to which to raise this Decimal.\r\n *\r\n */\r\nP.toPower = P.pow = function (y) {\r\n  var e, k, pr, r, rm, s,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    yn = +(y = new Ctor(y));\r\n\r\n  // Either ±Infinity, NaN or ±0?\r\n  if (!x.d || !y.d || !x.d[0] || !y.d[0]) return new Ctor(mathpow(+x, yn));\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (x.eq(1)) return x;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (y.eq(1)) return finalise(x, pr, rm);\r\n\r\n  // y exponent\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  // If y is a small integer use the 'exponentiation by squaring' algorithm.\r\n  if (e >= y.d.length - 1 && (k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\r\n    r = intPow(Ctor, x, k, pr);\r\n    return y.s < 0 ? new Ctor(1).div(r) : finalise(r, pr, rm);\r\n  }\r\n\r\n  s = x.s;\r\n\r\n  // if x is negative\r\n  if (s < 0) {\r\n\r\n    // if y is not an integer\r\n    if (e < y.d.length - 1) return new Ctor(NaN);\r\n\r\n    // Result is positive if x is negative and the last digit of integer y is even.\r\n    if ((y.d[e] & 1) == 0) s = 1;\r\n\r\n    // if x.eq(-1)\r\n    if (x.e == 0 && x.d[0] == 1 && x.d.length == 1) {\r\n      x.s = s;\r\n      return x;\r\n    }\r\n  }\r\n\r\n  // Estimate result exponent.\r\n  // x^y = 10^e,  where e = y * log10(x)\r\n  // log10(x) = log10(x_significand) + x_exponent\r\n  // log10(x_significand) = ln(x_significand) / ln(10)\r\n  k = mathpow(+x, yn);\r\n  e = k == 0 || !isFinite(k)\r\n    ? mathfloor(yn * (Math.log('0.' + digitsToString(x.d)) / Math.LN10 + x.e + 1))\r\n    : new Ctor(k + '').e;\r\n\r\n  // Exponent estimate may be incorrect e.g. x: 0.999999999999999999, y: 2.29, e: 0, r.e: -1.\r\n\r\n  // Overflow/underflow?\r\n  if (e > Ctor.maxE + 1 || e < Ctor.minE - 1) return new Ctor(e > 0 ? s / 0 : 0);\r\n\r\n  external = false;\r\n  Ctor.rounding = x.s = 1;\r\n\r\n  // Estimate the extra guard digits needed to ensure five correct rounding digits from\r\n  // naturalLogarithm(x). Example of failure without these extra digits (precision: 10):\r\n  // new Decimal(2.32456).pow('2087987436534566.46411')\r\n  // should be 1.162377823e+764914905173815, but is 1.162355823e+764914905173815\r\n  k = Math.min(12, (e + '').length);\r\n\r\n  // r = x^y = exp(y*ln(x))\r\n  r = naturalExponential(y.times(naturalLogarithm(x, pr + k)), pr);\r\n\r\n  // r may be Infinity, e.g. (0.9999999999999999).pow(-1e+40)\r\n  if (r.d) {\r\n\r\n    // Truncate to the required precision plus five rounding digits.\r\n    r = finalise(r, pr + 5, 1);\r\n\r\n    // If the rounding digits are [49]9999 or [50]0000 increase the precision by 10 and recalculate\r\n    // the result.\r\n    if (checkRoundingDigits(r.d, pr, rm)) {\r\n      e = pr + 10;\r\n\r\n      // Truncate to the increased precision plus five rounding digits.\r\n      r = finalise(naturalExponential(y.times(naturalLogarithm(x, e + k)), e), e + 5, 1);\r\n\r\n      // Check for 14 nines from the 2nd rounding digit (the first rounding digit may be 4 or 9).\r\n      if (+digitsToString(r.d).slice(pr + 1, pr + 15) + 1 == 1e14) {\r\n        r = finalise(r, pr + 1, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  r.s = s;\r\n  external = true;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), sd, rm);\r\n    str = finiteToString(x, sd <= x.e || x.e <= Ctor.toExpNeg, sd);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toSD() digits out of range: {sd}'\r\n * 'toSD() digits not an integer: {sd}'\r\n * 'toSD() rounding mode not an integer: {rm}'\r\n * 'toSD() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toSignificantDigits = P.toSD = function (sd, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  }\r\n\r\n  return finalise(new Ctor(x), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */\r\nP.toString = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal truncated to a whole number.\r\n *\r\n */\r\nP.truncated = P.trunc = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 1);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n * Unlike `toString`, negative zero will include the minus sign.\r\n *\r\n */\r\nP.valueOf = P.toJSON = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() ? '-' + str : str;\r\n};\r\n\r\n\r\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\r\n\r\n\r\n/*\r\n *  digitsToString           P.cubeRoot, P.logarithm, P.squareRoot, P.toFraction, P.toPower,\r\n *                           finiteToString, naturalExponential, naturalLogarithm\r\n *  checkInt32               P.toDecimalPlaces, P.toExponential, P.toFixed, P.toNearest,\r\n *                           P.toPrecision, P.toSignificantDigits, toStringBinary, random\r\n *  checkRoundingDigits      P.logarithm, P.toPower, naturalExponential, naturalLogarithm\r\n *  convertBase              toStringBinary, parseOther\r\n *  cos                      P.cos\r\n *  divide                   P.atanh, P.cubeRoot, P.dividedBy, P.dividedToIntegerBy,\r\n *                           P.logarithm, P.modulo, P.squareRoot, P.tan, P.tanh, P.toFraction,\r\n *                           P.toNearest, toStringBinary, naturalExponential, naturalLogarithm,\r\n *                           taylorSeries, atan2, parseOther\r\n *  finalise                 P.absoluteValue, P.atan, P.atanh, P.ceil, P.cos, P.cosh,\r\n *                           P.cubeRoot, P.dividedToIntegerBy, P.floor, P.logarithm, P.minus,\r\n *                           P.modulo, P.negated, P.plus, P.round, P.sin, P.sinh, P.squareRoot,\r\n *                           P.tan, P.times, P.toDecimalPlaces, P.toExponential, P.toFixed,\r\n *                           P.toNearest, P.toPower, P.toPrecision, P.toSignificantDigits,\r\n *                           P.truncated, divide, getLn10, getPi, naturalExponential,\r\n *                           naturalLogarithm, ceil, floor, round, trunc\r\n *  finiteToString           P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf,\r\n *                           toStringBinary\r\n *  getBase10Exponent        P.minus, P.plus, P.times, parseOther\r\n *  getLn10                  P.logarithm, naturalLogarithm\r\n *  getPi                    P.acos, P.asin, P.atan, toLessThanHalfPi, atan2\r\n *  getPrecision             P.precision, P.toFraction\r\n *  getZeroString            digitsToString, finiteToString\r\n *  intPow                   P.toPower, parseOther\r\n *  isOdd                    toLessThanHalfPi\r\n *  maxOrMin                 max, min\r\n *  naturalExponential       P.naturalExponential, P.toPower\r\n *  naturalLogarithm         P.acosh, P.asinh, P.atanh, P.logarithm, P.naturalLogarithm,\r\n *                           P.toPower, naturalExponential\r\n *  nonFiniteToString        finiteToString, toStringBinary\r\n *  parseDecimal             Decimal\r\n *  parseOther               Decimal\r\n *  sin                      P.sin\r\n *  taylorSeries             P.cosh, P.sinh, cos, sin\r\n *  toLessThanHalfPi         P.cos, P.sin\r\n *  toStringBinary           P.toBinary, P.toHexadecimal, P.toOctal\r\n *  truncate                 intPow\r\n *\r\n *  Throws:                  P.logarithm, P.precision, P.toFraction, checkInt32, getLn10, getPi,\r\n *                           naturalLogarithm, config, parseOther, random, Decimal\r\n */\r\n\r\n\r\nfunction digitsToString(d) {\r\n  var i, k, ws,\r\n    indexOfLastWord = d.length - 1,\r\n    str = '',\r\n    w = d[0];\r\n\r\n  if (indexOfLastWord > 0) {\r\n    str += w;\r\n    for (i = 1; i < indexOfLastWord; i++) {\r\n      ws = d[i] + '';\r\n      k = LOG_BASE - ws.length;\r\n      if (k) str += getZeroString(k);\r\n      str += ws;\r\n    }\r\n\r\n    w = d[i];\r\n    ws = w + '';\r\n    k = LOG_BASE - ws.length;\r\n    if (k) str += getZeroString(k);\r\n  } else if (w === 0) {\r\n    return '0';\r\n  }\r\n\r\n  // Remove trailing zeros of last w.\r\n  for (; w % 10 === 0;) w /= 10;\r\n\r\n  return str + w;\r\n}\r\n\r\n\r\nfunction checkInt32(i, min, max) {\r\n  if (i !== ~~i || i < min || i > max) {\r\n    throw Error(invalidArgument + i);\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Check 5 rounding digits if `repeating` is null, 4 otherwise.\r\n * `repeating == null` if caller is `log` or `pow`,\r\n * `repeating != null` if caller is `naturalLogarithm` or `naturalExponential`.\r\n */\r\nfunction checkRoundingDigits(d, i, rm, repeating) {\r\n  var di, k, r, rd;\r\n\r\n  // Get the length of the first word of the array d.\r\n  for (k = d[0]; k >= 10; k /= 10) --i;\r\n\r\n  // Is the rounding digit in the first word of d?\r\n  if (--i < 0) {\r\n    i += LOG_BASE;\r\n    di = 0;\r\n  } else {\r\n    di = Math.ceil((i + 1) / LOG_BASE);\r\n    i %= LOG_BASE;\r\n  }\r\n\r\n  // i is the index (0 - 6) of the rounding digit.\r\n  // E.g. if within the word 3487563 the first rounding digit is 5,\r\n  // then i = 4, k = 1000, rd = 3487563 % 1000 = 563\r\n  k = mathpow(10, LOG_BASE - i);\r\n  rd = d[di] % k | 0;\r\n\r\n  if (repeating == null) {\r\n    if (i < 3) {\r\n      if (i == 0) rd = rd / 100 | 0;\r\n      else if (i == 1) rd = rd / 10 | 0;\r\n      r = rm < 4 && rd == 99999 || rm > 3 && rd == 49999 || rd == 50000 || rd == 0;\r\n    } else {\r\n      r = (rm < 4 && rd + 1 == k || rm > 3 && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 100 | 0) == mathpow(10, i - 2) - 1 ||\r\n          (rd == k / 2 || rd == 0) && (d[di + 1] / k / 100 | 0) == 0;\r\n    }\r\n  } else {\r\n    if (i < 4) {\r\n      if (i == 0) rd = rd / 1000 | 0;\r\n      else if (i == 1) rd = rd / 100 | 0;\r\n      else if (i == 2) rd = rd / 10 | 0;\r\n      r = (repeating || rm < 4) && rd == 9999 || !repeating && rm > 3 && rd == 4999;\r\n    } else {\r\n      r = ((repeating || rm < 4) && rd + 1 == k ||\r\n      (!repeating && rm > 3) && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 1000 | 0) == mathpow(10, i - 3) - 1;\r\n    }\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n// Convert string of `baseIn` to an array of numbers of `baseOut`.\r\n// Eg. convertBase('255', 10, 16) returns [15, 15].\r\n// Eg. convertBase('ff', 16, 10) returns [2, 5, 5].\r\nfunction convertBase(str, baseIn, baseOut) {\r\n  var j,\r\n    arr = [0],\r\n    arrL,\r\n    i = 0,\r\n    strL = str.length;\r\n\r\n  for (; i < strL;) {\r\n    for (arrL = arr.length; arrL--;) arr[arrL] *= baseIn;\r\n    arr[0] += NUMERALS.indexOf(str.charAt(i++));\r\n    for (j = 0; j < arr.length; j++) {\r\n      if (arr[j] > baseOut - 1) {\r\n        if (arr[j + 1] === void 0) arr[j + 1] = 0;\r\n        arr[j + 1] += arr[j] / baseOut | 0;\r\n        arr[j] %= baseOut;\r\n      }\r\n    }\r\n  }\r\n\r\n  return arr.reverse();\r\n}\r\n\r\n\r\n/*\r\n * cos(x) = 1 - x^2/2! + x^4/4! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction cosine(Ctor, x) {\r\n  var k, len, y;\r\n\r\n  if (x.isZero()) return x;\r\n\r\n  // Argument reduction: cos(4x) = 8*(cos^4(x) - cos^2(x)) + 1\r\n  // i.e. cos(x) = 8*(cos^4(x/4) - cos^2(x/4)) + 1\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  len = x.d.length;\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    y = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    y = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  Ctor.precision += k;\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(y), new Ctor(1));\r\n\r\n  // Reverse argument reduction\r\n  for (var i = k; i--;) {\r\n    var cos2x = x.times(x);\r\n    x = cos2x.times(cos2x).minus(cos2x).times(8).plus(1);\r\n  }\r\n\r\n  Ctor.precision -= k;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Perform division in the specified base.\r\n */\r\nvar divide = (function () {\r\n\r\n  // Assumes non-zero x and k, and hence non-zero result.\r\n  function multiplyInteger(x, k, base) {\r\n    var temp,\r\n      carry = 0,\r\n      i = x.length;\r\n\r\n    for (x = x.slice(); i--;) {\r\n      temp = x[i] * k + carry;\r\n      x[i] = temp % base | 0;\r\n      carry = temp / base | 0;\r\n    }\r\n\r\n    if (carry) x.unshift(carry);\r\n\r\n    return x;\r\n  }\r\n\r\n  function compare(a, b, aL, bL) {\r\n    var i, r;\r\n\r\n    if (aL != bL) {\r\n      r = aL > bL ? 1 : -1;\r\n    } else {\r\n      for (i = r = 0; i < aL; i++) {\r\n        if (a[i] != b[i]) {\r\n          r = a[i] > b[i] ? 1 : -1;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    return r;\r\n  }\r\n\r\n  function subtract(a, b, aL, base) {\r\n    var i = 0;\r\n\r\n    // Subtract b from a.\r\n    for (; aL--;) {\r\n      a[aL] -= i;\r\n      i = a[aL] < b[aL] ? 1 : 0;\r\n      a[aL] = i * base + a[aL] - b[aL];\r\n    }\r\n\r\n    // Remove leading zeros.\r\n    for (; !a[0] && a.length > 1;) a.shift();\r\n  }\r\n\r\n  return function (x, y, pr, rm, dp, base) {\r\n    var cmp, e, i, k, logBase, more, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0,\r\n      yL, yz,\r\n      Ctor = x.constructor,\r\n      sign = x.s == y.s ? 1 : -1,\r\n      xd = x.d,\r\n      yd = y.d;\r\n\r\n    // Either NaN, Infinity or 0?\r\n    if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n      return new Ctor(// Return NaN if either NaN, or both Infinity or 0.\r\n        !x.s || !y.s || (xd ? yd && xd[0] == yd[0] : !yd) ? NaN :\r\n\r\n        // Return ±0 if x is 0 or y is ±Infinity, or return ±Infinity as y is 0.\r\n        xd && xd[0] == 0 || !yd ? sign * 0 : sign / 0);\r\n    }\r\n\r\n    if (base) {\r\n      logBase = 1;\r\n      e = x.e - y.e;\r\n    } else {\r\n      base = BASE;\r\n      logBase = LOG_BASE;\r\n      e = mathfloor(x.e / logBase) - mathfloor(y.e / logBase);\r\n    }\r\n\r\n    yL = yd.length;\r\n    xL = xd.length;\r\n    q = new Ctor(sign);\r\n    qd = q.d = [];\r\n\r\n    // Result exponent may be one less than e.\r\n    // The digit array of a Decimal from toStringBinary may have trailing zeros.\r\n    for (i = 0; yd[i] == (xd[i] || 0); i++);\r\n\r\n    if (yd[i] > (xd[i] || 0)) e--;\r\n\r\n    if (pr == null) {\r\n      sd = pr = Ctor.precision;\r\n      rm = Ctor.rounding;\r\n    } else if (dp) {\r\n      sd = pr + (x.e - y.e) + 1;\r\n    } else {\r\n      sd = pr;\r\n    }\r\n\r\n    if (sd < 0) {\r\n      qd.push(1);\r\n      more = true;\r\n    } else {\r\n\r\n      // Convert precision in number of base 10 digits to base 1e7 digits.\r\n      sd = sd / logBase + 2 | 0;\r\n      i = 0;\r\n\r\n      // divisor < 1e7\r\n      if (yL == 1) {\r\n        k = 0;\r\n        yd = yd[0];\r\n        sd++;\r\n\r\n        // k is the carry.\r\n        for (; (i < xL || k) && sd--; i++) {\r\n          t = k * base + (xd[i] || 0);\r\n          qd[i] = t / yd | 0;\r\n          k = t % yd | 0;\r\n        }\r\n\r\n        more = k || i < xL;\r\n\r\n      // divisor >= 1e7\r\n      } else {\r\n\r\n        // Normalise xd and yd so highest order digit of yd is >= base/2\r\n        k = base / (yd[0] + 1) | 0;\r\n\r\n        if (k > 1) {\r\n          yd = multiplyInteger(yd, k, base);\r\n          xd = multiplyInteger(xd, k, base);\r\n          yL = yd.length;\r\n          xL = xd.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xd.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL;) rem[remL++] = 0;\r\n\r\n        yz = yd.slice();\r\n        yz.unshift(0);\r\n        yd0 = yd[0];\r\n\r\n        if (yd[1] >= base / 2) ++yd0;\r\n\r\n        do {\r\n          k = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yd, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, k.\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n            // k will be how many times the divisor goes into the current remainder.\r\n            k = rem0 / yd0 | 0;\r\n\r\n            //  Algorithm:\r\n            //  1. product = divisor * trial digit (k)\r\n            //  2. if product > remainder: product -= divisor, k--\r\n            //  3. remainder -= product\r\n            //  4. if product was < remainder at 2:\r\n            //    5. compare new remainder and divisor\r\n            //    6. If remainder > divisor: remainder -= divisor, k++\r\n\r\n            if (k > 1) {\r\n              if (k >= base) k = base - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiplyInteger(yd, k, base);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              cmp = compare(prod, rem, prodL, remL);\r\n\r\n              // product > remainder.\r\n              if (cmp == 1) {\r\n                k--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yd, prodL, base);\r\n              }\r\n            } else {\r\n\r\n              // cmp is -1.\r\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\r\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\r\n              if (k == 0) cmp = k = 1;\r\n              prod = yd.slice();\r\n            }\r\n\r\n            prodL = prod.length;\r\n            if (prodL < remL) prod.unshift(0);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL, base);\r\n\r\n            // If product was < previous remainder.\r\n            if (cmp == -1) {\r\n              remL = rem.length;\r\n\r\n              // Compare divisor and new remainder.\r\n              cmp = compare(yd, rem, yL, remL);\r\n\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              if (cmp < 1) {\r\n                k++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yd, remL, base);\r\n              }\r\n            }\r\n\r\n            remL = rem.length;\r\n          } else if (cmp === 0) {\r\n            k++;\r\n            rem = [0];\r\n          }    // if cmp === 1, k will be 0\r\n\r\n          // Add the next digit, k, to the result array.\r\n          qd[i++] = k;\r\n\r\n          // Update the remainder.\r\n          if (cmp && rem[0]) {\r\n            rem[remL++] = xd[xi] || 0;\r\n          } else {\r\n            rem = [xd[xi]];\r\n            remL = 1;\r\n          }\r\n\r\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\r\n\r\n        more = rem[0] !== void 0;\r\n      }\r\n\r\n      // Leading zero?\r\n      if (!qd[0]) qd.shift();\r\n    }\r\n\r\n    // logBase is 1 when divide is being used for base conversion.\r\n    if (logBase == 1) {\r\n      q.e = e;\r\n      inexact = more;\r\n    } else {\r\n\r\n      // To calculate q.e, first get the number of digits of qd[0].\r\n      for (i = 1, k = qd[0]; k >= 10; k /= 10) i++;\r\n      q.e = i + e * logBase - 1;\r\n\r\n      finalise(q, dp ? pr + q.e + 1 : pr, rm, more);\r\n    }\r\n\r\n    return q;\r\n  };\r\n})();\r\n\r\n\r\n/*\r\n * Round `x` to `sd` significant digits using rounding mode `rm`.\r\n * Check for over/under-flow.\r\n */\r\n function finalise(x, sd, rm, isTruncated) {\r\n  var digits, i, j, k, rd, roundUp, w, xd, xdi,\r\n    Ctor = x.constructor;\r\n\r\n  // Don't round if sd is null or undefined.\r\n  out: if (sd != null) {\r\n    xd = x.d;\r\n\r\n    // Infinity/NaN.\r\n    if (!xd) return x;\r\n\r\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n    // w: the word of xd containing rd, a base 1e7 number.\r\n    // xdi: the index of w within xd.\r\n    // digits: the number of digits of w.\r\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\r\n    // they had leading zeros)\r\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\r\n\r\n    // Get the length of the first word of the digits array xd.\r\n    for (digits = 1, k = xd[0]; k >= 10; k /= 10) digits++;\r\n    i = sd - digits;\r\n\r\n    // Is the rounding digit in the first word of xd?\r\n    if (i < 0) {\r\n      i += LOG_BASE;\r\n      j = sd;\r\n      w = xd[xdi = 0];\r\n\r\n      // Get the rounding digit at index j of w.\r\n      rd = w / mathpow(10, digits - j - 1) % 10 | 0;\r\n    } else {\r\n      xdi = Math.ceil((i + 1) / LOG_BASE);\r\n      k = xd.length;\r\n      if (xdi >= k) {\r\n        if (isTruncated) {\r\n\r\n          // Needed by `naturalExponential`, `naturalLogarithm` and `squareRoot`.\r\n          for (; k++ <= xdi;) xd.push(0);\r\n          w = rd = 0;\r\n          digits = 1;\r\n          i %= LOG_BASE;\r\n          j = i - LOG_BASE + 1;\r\n        } else {\r\n          break out;\r\n        }\r\n      } else {\r\n        w = k = xd[xdi];\r\n\r\n        // Get the number of digits of w.\r\n        for (digits = 1; k >= 10; k /= 10) digits++;\r\n\r\n        // Get the index of rd within w.\r\n        i %= LOG_BASE;\r\n\r\n        // Get the index of rd within w, adjusted for leading zeros.\r\n        // The number of leading zeros of w is given by LOG_BASE - digits.\r\n        j = i - LOG_BASE + digits;\r\n\r\n        // Get the rounding digit at index j of w.\r\n        rd = j < 0 ? 0 : w / mathpow(10, digits - j - 1) % 10 | 0;\r\n      }\r\n    }\r\n\r\n    // Are there any non-zero digits after the rounding digit?\r\n    isTruncated = isTruncated || sd < 0 ||\r\n      xd[xdi + 1] !== void 0 || (j < 0 ? w : w % mathpow(10, digits - j - 1));\r\n\r\n    // The expression `w % mathpow(10, digits - j - 1)` returns all the digits of w to the right\r\n    // of the digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression\r\n    // will give 714.\r\n\r\n    roundUp = rm < 4\r\n      ? (rd || isTruncated) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n      : rd > 5 || rd == 5 && (rm == 4 || isTruncated || rm == 6 &&\r\n\r\n        // Check whether the digit to the left of the rounding digit is odd.\r\n        ((i > 0 ? j > 0 ? w / mathpow(10, digits - j) : 0 : xd[xdi - 1]) % 10) & 1 ||\r\n          rm == (x.s < 0 ? 8 : 7));\r\n\r\n    if (sd < 1 || !xd[0]) {\r\n      xd.length = 0;\r\n      if (roundUp) {\r\n\r\n        // Convert sd to decimal places.\r\n        sd -= x.e + 1;\r\n\r\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\r\n        x.e = -sd || 0;\r\n      } else {\r\n\r\n        // Zero.\r\n        xd[0] = x.e = 0;\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n    // Remove excess digits.\r\n    if (i == 0) {\r\n      xd.length = xdi;\r\n      k = 1;\r\n      xdi--;\r\n    } else {\r\n      xd.length = xdi + 1;\r\n      k = mathpow(10, LOG_BASE - i);\r\n\r\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n      // j > 0 means i > number of leading zeros of w.\r\n      xd[xdi] = j > 0 ? (w / mathpow(10, digits - j) % mathpow(10, j) | 0) * k : 0;\r\n    }\r\n\r\n    if (roundUp) {\r\n      for (;;) {\r\n\r\n        // Is the digit to be rounded up in the first word of xd?\r\n        if (xdi == 0) {\r\n\r\n          // i will be the length of xd[0] before k is added.\r\n          for (i = 1, j = xd[0]; j >= 10; j /= 10) i++;\r\n          j = xd[0] += k;\r\n          for (k = 1; j >= 10; j /= 10) k++;\r\n\r\n          // if i != k the length has increased.\r\n          if (i != k) {\r\n            x.e++;\r\n            if (xd[0] == BASE) xd[0] = 1;\r\n          }\r\n\r\n          break;\r\n        } else {\r\n          xd[xdi] += k;\r\n          if (xd[xdi] != BASE) break;\r\n          xd[xdi--] = 0;\r\n          k = 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\r\n  }\r\n\r\n  if (external) {\r\n\r\n    // Overflow?\r\n    if (x.e > Ctor.maxE) {\r\n\r\n      // Infinity.\r\n      x.d = null;\r\n      x.e = NaN;\r\n\r\n    // Underflow?\r\n    } else if (x.e < Ctor.minE) {\r\n\r\n      // Zero.\r\n      x.e = 0;\r\n      x.d = [0];\r\n      // Ctor.underflow = true;\r\n    } // else Ctor.underflow = false;\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\nfunction finiteToString(x, isExp, sd) {\r\n  if (!x.isFinite()) return nonFiniteToString(x);\r\n  var k,\r\n    e = x.e,\r\n    str = digitsToString(x.d),\r\n    len = str.length;\r\n\r\n  if (isExp) {\r\n    if (sd && (k = sd - len) > 0) {\r\n      str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\r\n    } else if (len > 1) {\r\n      str = str.charAt(0) + '.' + str.slice(1);\r\n    }\r\n\r\n    str = str + (x.e < 0 ? 'e' : 'e+') + x.e;\r\n  } else if (e < 0) {\r\n    str = '0.' + getZeroString(-e - 1) + str;\r\n    if (sd && (k = sd - len) > 0) str += getZeroString(k);\r\n  } else if (e >= len) {\r\n    str += getZeroString(e + 1 - len);\r\n    if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\r\n  } else {\r\n    if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\r\n    if (sd && (k = sd - len) > 0) {\r\n      if (e + 1 === len) str += '.';\r\n      str += getZeroString(k);\r\n    }\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\n\r\n// Calculate the base 10 exponent from the base 1e7 exponent.\r\nfunction getBase10Exponent(digits, e) {\r\n  var w = digits[0];\r\n\r\n  // Add the number of digits of the first word of the digits array.\r\n  for ( e *= LOG_BASE; w >= 10; w /= 10) e++;\r\n  return e;\r\n}\r\n\r\n\r\nfunction getLn10(Ctor, sd, pr) {\r\n  if (sd > LN10_PRECISION) {\r\n\r\n    // Reset global state in case the exception is caught.\r\n    external = true;\r\n    if (pr) Ctor.precision = pr;\r\n    throw Error(precisionLimitExceeded);\r\n  }\r\n  return finalise(new Ctor(LN10), sd, 1, true);\r\n}\r\n\r\n\r\nfunction getPi(Ctor, sd, rm) {\r\n  if (sd > PI_PRECISION) throw Error(precisionLimitExceeded);\r\n  return finalise(new Ctor(PI), sd, rm, true);\r\n}\r\n\r\n\r\nfunction getPrecision(digits) {\r\n  var w = digits.length - 1,\r\n    len = w * LOG_BASE + 1;\r\n\r\n  w = digits[w];\r\n\r\n  // If non-zero...\r\n  if (w) {\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    for (; w % 10 == 0; w /= 10) len--;\r\n\r\n    // Add the number of digits of the first word.\r\n    for (w = digits[0]; w >= 10; w /= 10) len++;\r\n  }\r\n\r\n  return len;\r\n}\r\n\r\n\r\nfunction getZeroString(k) {\r\n  var zs = '';\r\n  for (; k--;) zs += '0';\r\n  return zs;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of Decimal `x` to the power `n`, where `n` is an\r\n * integer of type number.\r\n *\r\n * Implements 'exponentiation by squaring'. Called by `pow` and `parseOther`.\r\n *\r\n */\r\nfunction intPow(Ctor, x, n, pr) {\r\n  var isTruncated,\r\n    r = new Ctor(1),\r\n\r\n    // Max n of 9007199254740991 takes 53 loop iterations.\r\n    // Maximum digits array length; leaves [28, 34] guard digits.\r\n    k = Math.ceil(pr / LOG_BASE + 4);\r\n\r\n  external = false;\r\n\r\n  for (;;) {\r\n    if (n % 2) {\r\n      r = r.times(x);\r\n      if (truncate(r.d, k)) isTruncated = true;\r\n    }\r\n\r\n    n = mathfloor(n / 2);\r\n    if (n === 0) {\r\n\r\n      // To ensure correct rounding when r.d is truncated, increment the last word if it is zero.\r\n      n = r.d.length - 1;\r\n      if (isTruncated && r.d[n] === 0) ++r.d[n];\r\n      break;\r\n    }\r\n\r\n    x = x.times(x);\r\n    truncate(x.d, k);\r\n  }\r\n\r\n  external = true;\r\n\r\n  return r;\r\n}\r\n\r\n\r\nfunction isOdd(n) {\r\n  return n.d[n.d.length - 1] & 1;\r\n}\r\n\r\n\r\n/*\r\n * Handle `max` (`n` is -1) and `min` (`n` is 1).\r\n */\r\nfunction maxOrMin(Ctor, args, n) {\r\n  var k, y,\r\n    x = new Ctor(args[0]),\r\n    i = 0;\r\n\r\n  for (; ++i < args.length;) {\r\n    y = new Ctor(args[i]);\r\n\r\n    // NaN?\r\n    if (!y.s) {\r\n      x = y;\r\n      break;\r\n    }\r\n\r\n    k = x.cmp(y);\r\n\r\n    if (k === n || k === 0 && x.s === n) {\r\n      x = y;\r\n    }\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * Max integer argument: exp('20723265836946413') = 6.3e+9000000000000000\r\n * Min integer argument: exp('-20723265836946411') = 1.2e-9000000000000000\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(Infinity)  = Infinity\r\n *  exp(-Infinity) = 0\r\n *  exp(NaN)       = NaN\r\n *  exp(±0)        = 1\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n *  The result will always be correctly rounded.\r\n *\r\n */\r\nfunction naturalExponential(x, sd) {\r\n  var denominator, guard, j, pow, sum, t, wpr,\r\n    rep = 0,\r\n    i = 0,\r\n    k = 0,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // 0/NaN/Infinity?\r\n  if (!x.d || !x.d[0] || x.e > 17) {\r\n\r\n    return new Ctor(x.d\r\n      ? !x.d[0] ? 1 : x.s < 0 ? 0 : 1 / 0\r\n      : x.s ? x.s < 0 ? 0 : x : 0 / 0);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  t = new Ctor(0.03125);\r\n\r\n  // while abs(x) >= 0.1\r\n  while (x.e > -2) {\r\n\r\n    // x = x / 2^5\r\n    x = x.times(t);\r\n    k += 5;\r\n  }\r\n\r\n  // Use 2 * log10(2^k) + 5 (empirically derived) to estimate the increase in precision\r\n  // necessary to ensure the first 4 rounding digits are correct.\r\n  guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\r\n  wpr += guard;\r\n  denominator = pow = sum = new Ctor(1);\r\n  Ctor.precision = wpr;\r\n\r\n  for (;;) {\r\n    pow = finalise(pow.times(x), wpr, 1);\r\n    denominator = denominator.times(++i);\r\n    t = sum.plus(divide(pow, denominator, wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      j = k;\r\n      while (j--) sum = finalise(sum.times(sum), wpr, 1);\r\n\r\n      // Check to see if the first 4 rounding digits are [49]999.\r\n      // If so, repeat the summation with a higher precision, otherwise\r\n      // e.g. with precision: 18, rounding: 1\r\n      // exp(18.404272462595034083567793919843761) = 98372560.1229999999 (should be 98372560.123)\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n\r\n        if (rep < 3 && checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += 10;\r\n          denominator = pow = t = new Ctor(1);\r\n          i = 0;\r\n          rep++;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n *  ln(-n)        = NaN\r\n *  ln(0)         = -Infinity\r\n *  ln(-0)        = -Infinity\r\n *  ln(1)         = 0\r\n *  ln(Infinity)  = Infinity\r\n *  ln(-Infinity) = NaN\r\n *  ln(NaN)       = NaN\r\n *\r\n *  ln(n) (n != 1) is non-terminating.\r\n *\r\n */\r\nfunction naturalLogarithm(y, sd) {\r\n  var c, c0, denominator, e, numerator, rep, sum, t, wpr, x1, x2,\r\n    n = 1,\r\n    guard = 10,\r\n    x = y,\r\n    xd = x.d,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // Is x negative or Infinity, NaN, 0 or 1?\r\n  if (x.s < 0 || !xd || !xd[0] || !x.e && xd[0] == 1 && xd.length == 1) {\r\n    return new Ctor(xd && !xd[0] ? -1 / 0 : x.s != 1 ? NaN : xd ? 0 : x);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  Ctor.precision = wpr += guard;\r\n  c = digitsToString(xd);\r\n  c0 = c.charAt(0);\r\n\r\n  if (Math.abs(e = x.e) < 1.5e15) {\r\n\r\n    // Argument reduction.\r\n    // The series converges faster the closer the argument is to 1, so using\r\n    // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\r\n    // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\r\n    // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\r\n    // later be divided by this number, then separate out the power of 10 using\r\n    // ln(a*10^b) = ln(a) + b*ln(10).\r\n\r\n    // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\r\n    //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\r\n    // max n is 6 (gives 0.7 - 1.3)\r\n    while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\r\n      x = x.times(y);\r\n      c = digitsToString(x.d);\r\n      c0 = c.charAt(0);\r\n      n++;\r\n    }\r\n\r\n    e = x.e;\r\n\r\n    if (c0 > 1) {\r\n      x = new Ctor('0.' + c);\r\n      e++;\r\n    } else {\r\n      x = new Ctor(c0 + '.' + c.slice(1));\r\n    }\r\n  } else {\r\n\r\n    // The argument reduction method above may result in overflow if the argument y is a massive\r\n    // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\r\n    // function using ln(x*10^e) = ln(x) + e*ln(10).\r\n    t = getLn10(Ctor, wpr + 2, pr).times(e + '');\r\n    x = naturalLogarithm(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\r\n    Ctor.precision = pr;\r\n\r\n    return sd == null ? finalise(x, pr, rm, external = true) : x;\r\n  }\r\n\r\n  // x1 is x reduced to a value near 1.\r\n  x1 = x;\r\n\r\n  // Taylor series.\r\n  // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\r\n  // where x = (y - 1)/(y + 1)    (|x| < 1)\r\n  sum = numerator = x = divide(x.minus(1), x.plus(1), wpr, 1);\r\n  x2 = finalise(x.times(x), wpr, 1);\r\n  denominator = 3;\r\n\r\n  for (;;) {\r\n    numerator = finalise(numerator.times(x2), wpr, 1);\r\n    t = sum.plus(divide(numerator, new Ctor(denominator), wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      sum = sum.times(2);\r\n\r\n      // Reverse the argument reduction. Check that e is not 0 because, besides preventing an\r\n      // unnecessary calculation, -0 + 0 = +0 and to ensure correct rounding -0 needs to stay -0.\r\n      if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\r\n      sum = divide(sum, new Ctor(n), wpr, 1);\r\n\r\n      // Is rm > 3 and the first 4 rounding digits 4999, or rm < 4 (or the summation has\r\n      // been repeated previously) and the first 4 rounding digits 9999?\r\n      // If so, restart the summation with a higher precision, otherwise\r\n      // e.g. with precision: 12, rounding: 1\r\n      // ln(135520028.6126091714265381533) = 18.7246299999 when it should be 18.72463.\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n        if (checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += guard;\r\n          t = numerator = x = divide(x1.minus(1), x1.plus(1), wpr, 1);\r\n          x2 = finalise(x.times(x), wpr, 1);\r\n          denominator = rep = 1;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n    denominator += 2;\r\n  }\r\n}\r\n\r\n\r\n// ±Infinity, NaN.\r\nfunction nonFiniteToString(x) {\r\n  // Unsigned.\r\n  return String(x.s * x.s / 0);\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */\r\nfunction parseDecimal(x, str) {\r\n  var e, i, len;\r\n\r\n  // TODO BigInt str: no need to check for decimal point, exponential form or leading zeros.\r\n  // Decimal point?\r\n  if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = str.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +str.slice(i + 1);\r\n    str = str.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = str.length;\r\n  }\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n  // Determine trailing zeros.\r\n  for (len = str.length; str.charCodeAt(len - 1) === 48; --len);\r\n  str = str.slice(i, len);\r\n\r\n  if (str) {\r\n    len -= i;\r\n    x.e = e = e - i - 1;\r\n    x.d = [];\r\n\r\n    // Transform base\r\n\r\n    // e is the base 10 exponent.\r\n    // i is where to slice str to get the first word of the digits array.\r\n    i = (e + 1) % LOG_BASE;\r\n    if (e < 0) i += LOG_BASE;\r\n\r\n    if (i < len) {\r\n      if (i) x.d.push(+str.slice(0, i));\r\n      for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\r\n      str = str.slice(i);\r\n      i = LOG_BASE - str.length;\r\n    } else {\r\n      i -= len;\r\n    }\r\n\r\n    for (; i--;) str += '0';\r\n    x.d.push(+str);\r\n\r\n    if (external) {\r\n\r\n      // Overflow?\r\n      if (x.e > x.constructor.maxE) {\r\n\r\n        // Infinity.\r\n        x.d = null;\r\n        x.e = NaN;\r\n\r\n      // Underflow?\r\n      } else if (x.e < x.constructor.minE) {\r\n\r\n        // Zero.\r\n        x.e = 0;\r\n        x.d = [0];\r\n        // x.constructor.underflow = true;\r\n      } // else x.constructor.underflow = false;\r\n    }\r\n  } else {\r\n\r\n    // Zero.\r\n    x.e = 0;\r\n    x.d = [0];\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from a string `str`, which is not a decimal value.\r\n */\r\nfunction parseOther(x, str) {\r\n  var base, Ctor, divisor, i, isFloat, len, p, xd, xe;\r\n\r\n  if (str.indexOf('_') > -1) {\r\n    str = str.replace(/(\\d)_(?=\\d)/g, '$1');\r\n    if (isDecimal.test(str)) return parseDecimal(x, str);\r\n  } else if (str === 'Infinity' || str === 'NaN') {\r\n    if (!+str) x.s = NaN;\r\n    x.e = NaN;\r\n    x.d = null;\r\n    return x;\r\n  }\r\n\r\n  if (isHex.test(str))  {\r\n    base = 16;\r\n    str = str.toLowerCase();\r\n  } else if (isBinary.test(str))  {\r\n    base = 2;\r\n  } else if (isOctal.test(str))  {\r\n    base = 8;\r\n  } else {\r\n    throw Error(invalidArgument + str);\r\n  }\r\n\r\n  // Is there a binary exponent part?\r\n  i = str.search(/p/i);\r\n\r\n  if (i > 0) {\r\n    p = +str.slice(i + 1);\r\n    str = str.substring(2, i);\r\n  } else {\r\n    str = str.slice(2);\r\n  }\r\n\r\n  // Convert `str` as an integer then divide the result by `base` raised to a power such that the\r\n  // fraction part will be restored.\r\n  i = str.indexOf('.');\r\n  isFloat = i >= 0;\r\n  Ctor = x.constructor;\r\n\r\n  if (isFloat) {\r\n    str = str.replace('.', '');\r\n    len = str.length;\r\n    i = len - i;\r\n\r\n    // log[10](16) = 1.2041... , log[10](88) = 1.9444....\r\n    divisor = intPow(Ctor, new Ctor(base), i, i * 2);\r\n  }\r\n\r\n  xd = convertBase(str, base, BASE);\r\n  xe = xd.length - 1;\r\n\r\n  // Remove trailing zeros.\r\n  for (i = xe; xd[i] === 0; --i) xd.pop();\r\n  if (i < 0) return new Ctor(x.s * 0);\r\n  x.e = getBase10Exponent(xd, xe);\r\n  x.d = xd;\r\n  external = false;\r\n\r\n  // At what precision to perform the division to ensure exact conversion?\r\n  // maxDecimalIntegerPartDigitCount = ceil(log[10](b) * otherBaseIntegerPartDigitCount)\r\n  // log[10](2) = 0.30103, log[10](8) = 0.90309, log[10](16) = 1.20412\r\n  // E.g. ceil(1.2 * 3) = 4, so up to 4 decimal digits are needed to represent 3 hex int digits.\r\n  // maxDecimalFractionPartDigitCount = {Hex:4|Oct:3|Bin:1} * otherBaseFractionPartDigitCount\r\n  // Therefore using 4 * the number of digits of str will always be enough.\r\n  if (isFloat) x = divide(x, divisor, len * 4);\r\n\r\n  // Multiply by the binary exponent part if present.\r\n  if (p) x = x.times(Math.abs(p) < 54 ? mathpow(2, p) : Decimal.pow(2, p));\r\n  external = true;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction sine(Ctor, x) {\r\n  var k,\r\n    len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    return x.isZero() ? x : taylorSeries(Ctor, 2, x, x);\r\n  }\r\n\r\n  // Argument reduction: sin(5x) = 16*sin^5(x) - 20*sin^3(x) + 5*sin(x)\r\n  // i.e. sin(x) = 16*sin^5(x/5) - 20*sin^3(x/5) + 5*sin(x/5)\r\n  // and  sin(x) = sin(x/5)(5 + sin^2(x/5)(16sin^2(x/5) - 20))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  k = 1.4 * Math.sqrt(len);\r\n  k = k > 16 ? 16 : k | 0;\r\n\r\n  x = x.times(1 / tinyPow(5, k));\r\n  x = taylorSeries(Ctor, 2, x, x);\r\n\r\n  // Reverse argument reduction\r\n  var sin2_x,\r\n    d5 = new Ctor(5),\r\n    d16 = new Ctor(16),\r\n    d20 = new Ctor(20);\r\n  for (; k--;) {\r\n    sin2_x = x.times(x);\r\n    x = x.times(d5.plus(sin2_x.times(d16.times(sin2_x).minus(d20))));\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n// Calculate Taylor series for `cos`, `cosh`, `sin` and `sinh`.\r\nfunction taylorSeries(Ctor, n, x, y, isHyperbolic) {\r\n  var j, t, u, x2,\r\n    i = 1,\r\n    pr = Ctor.precision,\r\n    k = Math.ceil(pr / LOG_BASE);\r\n\r\n  external = false;\r\n  x2 = x.times(x);\r\n  u = new Ctor(y);\r\n\r\n  for (;;) {\r\n    t = divide(u.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    u = isHyperbolic ? y.plus(t) : y.minus(t);\r\n    y = divide(t.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    t = u.plus(y);\r\n\r\n    if (t.d[k] !== void 0) {\r\n      for (j = k; t.d[j] === u.d[j] && j--;);\r\n      if (j == -1) break;\r\n    }\r\n\r\n    j = u;\r\n    u = y;\r\n    y = t;\r\n    t = j;\r\n    i++;\r\n  }\r\n\r\n  external = true;\r\n  t.d.length = k + 1;\r\n\r\n  return t;\r\n}\r\n\r\n\r\n// Exponent e must be positive and non-zero.\r\nfunction tinyPow(b, e) {\r\n  var n = b;\r\n  while (--e) n *= b;\r\n  return n;\r\n}\r\n\r\n\r\n// Return the absolute value of `x` reduced to less than or equal to half pi.\r\nfunction toLessThanHalfPi(Ctor, x) {\r\n  var t,\r\n    isNeg = x.s < 0,\r\n    pi = getPi(Ctor, Ctor.precision, 1),\r\n    halfPi = pi.times(0.5);\r\n\r\n  x = x.abs();\r\n\r\n  if (x.lte(halfPi)) {\r\n    quadrant = isNeg ? 4 : 1;\r\n    return x;\r\n  }\r\n\r\n  t = x.divToInt(pi);\r\n\r\n  if (t.isZero()) {\r\n    quadrant = isNeg ? 3 : 2;\r\n  } else {\r\n    x = x.minus(t.times(pi));\r\n\r\n    // 0 <= x < pi\r\n    if (x.lte(halfPi)) {\r\n      quadrant = isOdd(t) ? (isNeg ? 2 : 3) : (isNeg ? 4 : 1);\r\n      return x;\r\n    }\r\n\r\n    quadrant = isOdd(t) ? (isNeg ? 1 : 4) : (isNeg ? 3 : 2);\r\n  }\r\n\r\n  return x.minus(pi).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return the value of Decimal `x` as a string in base `baseOut`.\r\n *\r\n * If the optional `sd` argument is present include a binary exponent suffix.\r\n */\r\nfunction toStringBinary(x, baseOut, sd, rm) {\r\n  var base, e, i, k, len, roundUp, str, xd, y,\r\n    Ctor = x.constructor,\r\n    isExp = sd !== void 0;\r\n\r\n  if (isExp) {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  } else {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  }\r\n\r\n  if (!x.isFinite()) {\r\n    str = nonFiniteToString(x);\r\n  } else {\r\n    str = finiteToString(x);\r\n    i = str.indexOf('.');\r\n\r\n    // Use exponential notation according to `toExpPos` and `toExpNeg`? No, but if required:\r\n    // maxBinaryExponent = floor((decimalExponent + 1) * log[2](10))\r\n    // minBinaryExponent = floor(decimalExponent * log[2](10))\r\n    // log[2](10) = 3.321928094887362347870319429489390175864\r\n\r\n    if (isExp) {\r\n      base = 2;\r\n      if (baseOut == 16) {\r\n        sd = sd * 4 - 3;\r\n      } else if (baseOut == 8) {\r\n        sd = sd * 3 - 2;\r\n      }\r\n    } else {\r\n      base = baseOut;\r\n    }\r\n\r\n    // Convert the number as an integer then divide the result by its base raised to a power such\r\n    // that the fraction part will be restored.\r\n\r\n    // Non-integer.\r\n    if (i >= 0) {\r\n      str = str.replace('.', '');\r\n      y = new Ctor(1);\r\n      y.e = str.length - i;\r\n      y.d = convertBase(finiteToString(y), 10, base);\r\n      y.e = y.d.length;\r\n    }\r\n\r\n    xd = convertBase(str, 10, base);\r\n    e = len = xd.length;\r\n\r\n    // Remove trailing zeros.\r\n    for (; xd[--len] == 0;) xd.pop();\r\n\r\n    if (!xd[0]) {\r\n      str = isExp ? '0p+0' : '0';\r\n    } else {\r\n      if (i < 0) {\r\n        e--;\r\n      } else {\r\n        x = new Ctor(x);\r\n        x.d = xd;\r\n        x.e = e;\r\n        x = divide(x, y, sd, rm, 0, base);\r\n        xd = x.d;\r\n        e = x.e;\r\n        roundUp = inexact;\r\n      }\r\n\r\n      // The rounding digit, i.e. the digit after the digit that may be rounded up.\r\n      i = xd[sd];\r\n      k = base / 2;\r\n      roundUp = roundUp || xd[sd + 1] !== void 0;\r\n\r\n      roundUp = rm < 4\r\n        ? (i !== void 0 || roundUp) && (rm === 0 || rm === (x.s < 0 ? 3 : 2))\r\n        : i > k || i === k && (rm === 4 || roundUp || rm === 6 && xd[sd - 1] & 1 ||\r\n          rm === (x.s < 0 ? 8 : 7));\r\n\r\n      xd.length = sd;\r\n\r\n      if (roundUp) {\r\n\r\n        // Rounding up may mean the previous digit has to be rounded up and so on.\r\n        for (; ++xd[--sd] > base - 1;) {\r\n          xd[sd] = 0;\r\n          if (!sd) {\r\n            ++e;\r\n            xd.unshift(1);\r\n          }\r\n        }\r\n      }\r\n\r\n      // Determine trailing zeros.\r\n      for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n      // E.g. [4, 11, 15] becomes 4bf.\r\n      for (i = 0, str = ''; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n\r\n      // Add binary exponent suffix?\r\n      if (isExp) {\r\n        if (len > 1) {\r\n          if (baseOut == 16 || baseOut == 8) {\r\n            i = baseOut == 16 ? 4 : 3;\r\n            for (--len; len % i; len++) str += '0';\r\n            xd = convertBase(str, base, baseOut);\r\n            for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n            // xd[0] will always be be 1\r\n            for (i = 1, str = '1.'; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n          } else {\r\n            str = str.charAt(0) + '.' + str.slice(1);\r\n          }\r\n        }\r\n\r\n        str =  str + (e < 0 ? 'p' : 'p+') + e;\r\n      } else if (e < 0) {\r\n        for (; ++e;) str = '0' + str;\r\n        str = '0.' + str;\r\n      } else {\r\n        if (++e > len) for (e -= len; e-- ;) str += '0';\r\n        else if (e < len) str = str.slice(0, e) + '.' + str.slice(e);\r\n      }\r\n    }\r\n\r\n    str = (baseOut == 16 ? '0x' : baseOut == 2 ? '0b' : baseOut == 8 ? '0o' : '') + str;\r\n  }\r\n\r\n  return x.s < 0 ? '-' + str : str;\r\n}\r\n\r\n\r\n// Does not strip trailing zeros.\r\nfunction truncate(arr, len) {\r\n  if (arr.length > len) {\r\n    arr.length = len;\r\n    return true;\r\n  }\r\n}\r\n\r\n\r\n// Decimal methods\r\n\r\n\r\n/*\r\n *  abs\r\n *  acos\r\n *  acosh\r\n *  add\r\n *  asin\r\n *  asinh\r\n *  atan\r\n *  atanh\r\n *  atan2\r\n *  cbrt\r\n *  ceil\r\n *  clamp\r\n *  clone\r\n *  config\r\n *  cos\r\n *  cosh\r\n *  div\r\n *  exp\r\n *  floor\r\n *  hypot\r\n *  ln\r\n *  log\r\n *  log2\r\n *  log10\r\n *  max\r\n *  min\r\n *  mod\r\n *  mul\r\n *  pow\r\n *  random\r\n *  round\r\n *  set\r\n *  sign\r\n *  sin\r\n *  sinh\r\n *  sqrt\r\n *  sub\r\n *  sum\r\n *  tan\r\n *  tanh\r\n *  trunc\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction abs(x) {\r\n  return new this(x).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine in radians of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction acos(x) {\r\n  return new this(x).acos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction acosh(x) {\r\n  return new this(x).acosh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of `x` and `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction add(x, y) {\r\n  return new this(x).plus(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction asin(x) {\r\n  return new this(x).asin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction asinh(x) {\r\n  return new this(x).asinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction atan(x) {\r\n  return new this(x).atan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction atanh(x) {\r\n  return new this(x).atanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `y/x` in the range -pi to pi\r\n * (inclusive), rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi, pi]\r\n *\r\n * y {number|string|bigint|Decimal} The y-coordinate.\r\n * x {number|string|bigint|Decimal} The x-coordinate.\r\n *\r\n * atan2(±0, -0)               = ±pi\r\n * atan2(±0, +0)               = ±0\r\n * atan2(±0, -x)               = ±pi for x > 0\r\n * atan2(±0, x)                = ±0 for x > 0\r\n * atan2(-y, ±0)               = -pi/2 for y > 0\r\n * atan2(y, ±0)                = pi/2 for y > 0\r\n * atan2(±y, -Infinity)        = ±pi for finite y > 0\r\n * atan2(±y, +Infinity)        = ±0 for finite y > 0\r\n * atan2(±Infinity, x)         = ±pi/2 for finite x\r\n * atan2(±Infinity, -Infinity) = ±3*pi/4\r\n * atan2(±Infinity, +Infinity) = ±pi/4\r\n * atan2(NaN, x) = NaN\r\n * atan2(y, NaN) = NaN\r\n *\r\n */\r\nfunction atan2(y, x) {\r\n  y = new this(y);\r\n  x = new this(x);\r\n  var r,\r\n    pr = this.precision,\r\n    rm = this.rounding,\r\n    wpr = pr + 4;\r\n\r\n  // Either NaN\r\n  if (!y.s || !x.s) {\r\n    r = new this(NaN);\r\n\r\n  // Both ±Infinity\r\n  } else if (!y.d && !x.d) {\r\n    r = getPi(this, wpr, 1).times(x.s > 0 ? 0.25 : 0.75);\r\n    r.s = y.s;\r\n\r\n  // x is ±Infinity or y is ±0\r\n  } else if (!x.d || y.isZero()) {\r\n    r = x.s < 0 ? getPi(this, pr, rm) : new this(0);\r\n    r.s = y.s;\r\n\r\n  // y is ±Infinity or x is ±0\r\n  } else if (!y.d || x.isZero()) {\r\n    r = getPi(this, wpr, 1).times(0.5);\r\n    r.s = y.s;\r\n\r\n  // Both non-zero and finite\r\n  } else if (x.s < 0) {\r\n    this.precision = wpr;\r\n    this.rounding = 1;\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n    x = getPi(this, wpr, 1);\r\n    this.precision = pr;\r\n    this.rounding = rm;\r\n    r = y.s < 0 ? r.minus(x) : r.plus(x);\r\n  } else {\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cube root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction cbrt(x) {\r\n  return new this(x).cbrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using `ROUND_CEIL`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ceil(x) {\r\n  return finalise(x = new this(x), x.e + 1, 2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` clamped to the range delineated by `min` and `max`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction clamp(x, min, max) {\r\n  return new this(x).clamp(min, max);\r\n}\r\n\r\n\r\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *   maxE       {number}\r\n *   minE       {number}\r\n *   modulo     {number}\r\n *   crypto     {boolean|number}\r\n *   defaults   {true}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */\r\nfunction config(obj) {\r\n  if (!obj || typeof obj !== 'object') throw Error(decimalError + 'Object expected');\r\n  var i, p, v,\r\n    useDefaults = obj.defaults === true,\r\n    ps = [\r\n      'precision', 1, MAX_DIGITS,\r\n      'rounding', 0, 8,\r\n      'toExpNeg', -EXP_LIMIT, 0,\r\n      'toExpPos', 0, EXP_LIMIT,\r\n      'maxE', 0, EXP_LIMIT,\r\n      'minE', -EXP_LIMIT, 0,\r\n      'modulo', 0, 9\r\n    ];\r\n\r\n  for (i = 0; i < ps.length; i += 3) {\r\n    if (p = ps[i], useDefaults) this[p] = DEFAULTS[p];\r\n    if ((v = obj[p]) !== void 0) {\r\n      if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\r\n      else throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  if (p = 'crypto', useDefaults) this[p] = DEFAULTS[p];\r\n  if ((v = obj[p]) !== void 0) {\r\n    if (v === true || v === false || v === 0 || v === 1) {\r\n      if (v) {\r\n        if (typeof crypto != 'undefined' && crypto &&\r\n          (crypto.getRandomValues || crypto.randomBytes)) {\r\n          this[p] = true;\r\n        } else {\r\n          throw Error(cryptoUnavailable);\r\n        }\r\n      } else {\r\n        this[p] = false;\r\n      }\r\n    } else {\r\n      throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  return this;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cos(x) {\r\n  return new this(x).cos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of `x`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cosh(x) {\r\n  return new this(x).cosh();\r\n}\r\n\r\n\r\n/*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */\r\nfunction clone(obj) {\r\n  var i, p, ps;\r\n\r\n  /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * v {number|string|bigint|Decimal} A numeric value.\r\n   *\r\n   */\r\n  function Decimal(v) {\r\n    var e, i, t,\r\n      x = this;\r\n\r\n    // Decimal called without new.\r\n    if (!(x instanceof Decimal)) return new Decimal(v);\r\n\r\n    // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\r\n    // which points to Object.\r\n    x.constructor = Decimal;\r\n\r\n    if (isDecimalInstance(v)) {\r\n      x.s = v.s;\r\n\r\n      if (external) {\r\n        if (!v.d || v.e > Decimal.maxE) {\r\n\r\n          // Infinity.\r\n          x.e = NaN;\r\n          x.d = null;\r\n        } else if (v.e < Decimal.minE) {\r\n\r\n          // Zero.\r\n          x.e = 0;\r\n          x.d = [0];\r\n        } else {\r\n          x.e = v.e;\r\n          x.d = v.d.slice();\r\n        }\r\n      } else {\r\n        x.e = v.e;\r\n        x.d = v.d ? v.d.slice() : v.d;\r\n      }\r\n\r\n      return;\r\n    }\r\n\r\n    t = typeof v;\r\n\r\n    if (t === 'number') {\r\n      if (v === 0) {\r\n        x.s = 1 / v < 0 ? -1 : 1;\r\n        x.e = 0;\r\n        x.d = [0];\r\n        return;\r\n      }\r\n\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      // Fast path for small integers.\r\n      if (v === ~~v && v < 1e7) {\r\n        for (e = 0, i = v; i >= 10; i /= 10) e++;\r\n\r\n        if (external) {\r\n          if (e > Decimal.maxE) {\r\n            x.e = NaN;\r\n            x.d = null;\r\n          } else if (e < Decimal.minE) {\r\n            x.e = 0;\r\n            x.d = [0];\r\n          } else {\r\n            x.e = e;\r\n            x.d = [v];\r\n          }\r\n        } else {\r\n          x.e = e;\r\n          x.d = [v];\r\n        }\r\n\r\n        return;\r\n      }\r\n\r\n      // Infinity or NaN?\r\n      if (v * 0 !== 0) {\r\n        if (!v) x.s = NaN;\r\n        x.e = NaN;\r\n        x.d = null;\r\n        return;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    if (t === 'string') {\r\n      if ((i = v.charCodeAt(0)) === 45) {  // minus sign\r\n        v = v.slice(1);\r\n        x.s = -1;\r\n      } else {\r\n        if (i === 43) v = v.slice(1);  // plus sign\r\n        x.s = 1;\r\n      }\r\n\r\n      return isDecimal.test(v) ? parseDecimal(x, v) : parseOther(x, v);\r\n    }\r\n\r\n    if (t === 'bigint') {\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    throw Error(invalidArgument + v);\r\n  }\r\n\r\n  Decimal.prototype = P;\r\n\r\n  Decimal.ROUND_UP = 0;\r\n  Decimal.ROUND_DOWN = 1;\r\n  Decimal.ROUND_CEIL = 2;\r\n  Decimal.ROUND_FLOOR = 3;\r\n  Decimal.ROUND_HALF_UP = 4;\r\n  Decimal.ROUND_HALF_DOWN = 5;\r\n  Decimal.ROUND_HALF_EVEN = 6;\r\n  Decimal.ROUND_HALF_CEIL = 7;\r\n  Decimal.ROUND_HALF_FLOOR = 8;\r\n  Decimal.EUCLID = 9;\r\n\r\n  Decimal.config = Decimal.set = config;\r\n  Decimal.clone = clone;\r\n  Decimal.isDecimal = isDecimalInstance;\r\n\r\n  Decimal.abs = abs;\r\n  Decimal.acos = acos;\r\n  Decimal.acosh = acosh;        // ES6\r\n  Decimal.add = add;\r\n  Decimal.asin = asin;\r\n  Decimal.asinh = asinh;        // ES6\r\n  Decimal.atan = atan;\r\n  Decimal.atanh = atanh;        // ES6\r\n  Decimal.atan2 = atan2;\r\n  Decimal.cbrt = cbrt;          // ES6\r\n  Decimal.ceil = ceil;\r\n  Decimal.clamp = clamp;\r\n  Decimal.cos = cos;\r\n  Decimal.cosh = cosh;          // ES6\r\n  Decimal.div = div;\r\n  Decimal.exp = exp;\r\n  Decimal.floor = floor;\r\n  Decimal.hypot = hypot;        // ES6\r\n  Decimal.ln = ln;\r\n  Decimal.log = log;\r\n  Decimal.log10 = log10;        // ES6\r\n  Decimal.log2 = log2;          // ES6\r\n  Decimal.max = max;\r\n  Decimal.min = min;\r\n  Decimal.mod = mod;\r\n  Decimal.mul = mul;\r\n  Decimal.pow = pow;\r\n  Decimal.random = random;\r\n  Decimal.round = round;\r\n  Decimal.sign = sign;          // ES6\r\n  Decimal.sin = sin;\r\n  Decimal.sinh = sinh;          // ES6\r\n  Decimal.sqrt = sqrt;\r\n  Decimal.sub = sub;\r\n  Decimal.sum = sum;\r\n  Decimal.tan = tan;\r\n  Decimal.tanh = tanh;          // ES6\r\n  Decimal.trunc = trunc;        // ES6\r\n\r\n  if (obj === void 0) obj = {};\r\n  if (obj) {\r\n    if (obj.defaults !== true) {\r\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'maxE', 'minE', 'modulo', 'crypto'];\r\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\r\n    }\r\n  }\r\n\r\n  Decimal.config(obj);\r\n\r\n  return Decimal;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` divided by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction div(x, y) {\r\n  return new this(x).div(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The power to which to raise the base of the natural log.\r\n *\r\n */\r\nfunction exp(x) {\r\n  return new this(x).exp();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` round to an integer using `ROUND_FLOOR`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction floor(x) {\r\n  return finalise(x = new this(x), x.e + 1, 3);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of the sum of the squares of the arguments,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * hypot(a, b, ...) = sqrt(a^2 + b^2 + ...)\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction hypot() {\r\n  var i, n,\r\n    t = new this(0);\r\n\r\n  external = false;\r\n\r\n  for (i = 0; i < arguments.length;) {\r\n    n = new this(arguments[i++]);\r\n    if (!n.d) {\r\n      if (n.s) {\r\n        external = true;\r\n        return new this(1 / 0);\r\n      }\r\n      t = n;\r\n    } else if (t.d) {\r\n      t = t.plus(n.times(n));\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return t.sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return true if object is a Decimal instance (where Decimal is any Decimal constructor),\r\n * otherwise return false.\r\n *\r\n */\r\nfunction isDecimalInstance(obj) {\r\n  return obj instanceof Decimal || obj && obj.toStringTag === tag || false;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ln(x) {\r\n  return new this(x).ln();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the log of `x` to the base `y`, or to base 10 if no base\r\n * is specified, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * log[y](x)\r\n *\r\n * x {number|string|bigint|Decimal} The argument of the logarithm.\r\n * y {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nfunction log(x, y) {\r\n  return new this(x).log(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 2 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log2(x) {\r\n  return new this(x).log(2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 10 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log10(x) {\r\n  return new this(x).log(10);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction max() {\r\n  return maxOrMin(this, arguments, -1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction min() {\r\n  return maxOrMin(this, arguments, 1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` modulo `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mod(x, y) {\r\n  return new this(x).mod(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` multiplied by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mul(x, y) {\r\n  return new this(x).mul(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` raised to the power `y`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The base.\r\n * y {number|string|bigint|Decimal} The exponent.\r\n *\r\n */\r\nfunction pow(x, y) {\r\n  return new this(x).pow(y);\r\n}\r\n\r\n\r\n/*\r\n * Returns a new Decimal with a random value equal to or greater than 0 and less than 1, and with\r\n * `sd`, or `Decimal.precision` if `sd` is omitted, significant digits (or less if trailing zeros\r\n * are produced).\r\n *\r\n * [sd] {number} Significant digits. Integer, 0 to MAX_DIGITS inclusive.\r\n *\r\n */\r\nfunction random(sd) {\r\n  var d, e, k, n,\r\n    i = 0,\r\n    r = new this(1),\r\n    rd = [];\r\n\r\n  if (sd === void 0) sd = this.precision;\r\n  else checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n  k = Math.ceil(sd / LOG_BASE);\r\n\r\n  if (!this.crypto) {\r\n    for (; i < k;) rd[i++] = Math.random() * 1e7 | 0;\r\n\r\n  // Browsers supporting crypto.getRandomValues.\r\n  } else if (crypto.getRandomValues) {\r\n    d = crypto.getRandomValues(new Uint32Array(k));\r\n\r\n    for (; i < k;) {\r\n      n = d[i];\r\n\r\n      // 0 <= n < 4294967296\r\n      // Probability n >= 4.29e9, is 4967296 / 4294967296 = 0.00116 (1 in 865).\r\n      if (n >= 4.29e9) {\r\n        d[i] = crypto.getRandomValues(new Uint32Array(1))[0];\r\n      } else {\r\n\r\n        // 0 <= n <= 4289999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd[i++] = n % 1e7;\r\n      }\r\n    }\r\n\r\n  // Node.js supporting crypto.randomBytes.\r\n  } else if (crypto.randomBytes) {\r\n\r\n    // buffer\r\n    d = crypto.randomBytes(k *= 4);\r\n\r\n    for (; i < k;) {\r\n\r\n      // 0 <= n < 2147483648\r\n      n = d[i] + (d[i + 1] << 8) + (d[i + 2] << 16) + ((d[i + 3] & 0x7f) << 24);\r\n\r\n      // Probability n >= 2.14e9, is 7483648 / 2147483648 = 0.0035 (1 in 286).\r\n      if (n >= 2.14e9) {\r\n        crypto.randomBytes(4).copy(d, i);\r\n      } else {\r\n\r\n        // 0 <= n <= 2139999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd.push(n % 1e7);\r\n        i += 4;\r\n      }\r\n    }\r\n\r\n    i = k / 4;\r\n  } else {\r\n    throw Error(cryptoUnavailable);\r\n  }\r\n\r\n  k = rd[--i];\r\n  sd %= LOG_BASE;\r\n\r\n  // Convert trailing digits to zeros according to sd.\r\n  if (k && sd) {\r\n    n = mathpow(10, LOG_BASE - sd);\r\n    rd[i] = (k / n | 0) * n;\r\n  }\r\n\r\n  // Remove trailing words which are zero.\r\n  for (; rd[i] === 0; i--) rd.pop();\r\n\r\n  // Zero?\r\n  if (i < 0) {\r\n    e = 0;\r\n    rd = [0];\r\n  } else {\r\n    e = -1;\r\n\r\n    // Remove leading words which are zero and adjust exponent accordingly.\r\n    for (; rd[0] === 0; e -= LOG_BASE) rd.shift();\r\n\r\n    // Count the digits of the first word of rd to determine leading zeros.\r\n    for (k = 1, n = rd[0]; n >= 10; n /= 10) k++;\r\n\r\n    // Adjust the exponent for leading zeros of the first word of rd.\r\n    if (k < LOG_BASE) e -= LOG_BASE - k;\r\n  }\r\n\r\n  r.e = e;\r\n  r.d = rd;\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using rounding mode `rounding`.\r\n *\r\n * To emulate `Math.round`, set rounding to 7 (ROUND_HALF_CEIL).\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction round(x) {\r\n  return finalise(x = new this(x), x.e + 1, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if x > 0,\r\n *  -1    if x < 0,\r\n *   0    if x is 0,\r\n *  -0    if x is -0,\r\n *   NaN  otherwise\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sign(x) {\r\n  x = new this(x);\r\n  return x.d ? (x.d[0] ? x.s : 0 * x.s) : x.s || NaN;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of `x`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sin(x) {\r\n  return new this(x).sin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sinh(x) {\r\n  return new this(x).sinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sqrt(x) {\r\n  return new this(x).sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` minus `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sub(x, y) {\r\n  return new this(x).sub(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of the arguments, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * Only the result is rounded, not the intermediate calculations.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sum() {\r\n  var i = 0,\r\n    args = arguments,\r\n    x = new this(args[i]);\r\n\r\n  external = false;\r\n  for (; x.s && ++i < args.length;) x = x.plus(args[i]);\r\n  external = true;\r\n\r\n  return finalise(x, this.precision, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tan(x) {\r\n  return new this(x).tan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tanh(x) {\r\n  return new this(x).tanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` truncated to an integer.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction trunc(x) {\r\n  return finalise(x = new this(x), x.e + 1, 1);\r\n}\r\n\r\n\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toString;\r\nP[Symbol.toStringTag] = 'Decimal';\r\n\r\n// Create and configure initial Decimal constructor.\r\nexport var Decimal = P.constructor = clone(DEFAULTS);\r\n\r\n// Create the internal constants from their string values.\r\nLN10 = new Decimal(LN10);\r\nPI = new Decimal(PI);\r\n\r\nexport default Decimal;\r\n"], "mappings": ";;;AAcA,IAAI,YAAY;AAAhB,IAIE,aAAa;AAJf,IAOE,WAAW;AAPb,IAUE,OAAO;AAVT,IAaE,KAAK;AAbP,IAiBE,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBX,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeV,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIR,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,UAAW;AAAA;AAAA;AAAA;AAAA,EAIX,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIP,MAAM;AAAA;AAAA;AAAA,EAGN,QAAQ;AAAA;AACV;AA5EF,IAkFE;AAlFF,IAkFW;AAlFX,IAmFE,WAAW;AAnFb,IAqFE,eAAe;AArFjB,IAsFE,kBAAkB,eAAe;AAtFnC,IAuFE,yBAAyB,eAAe;AAvF1C,IAwFE,oBAAoB,eAAe;AAxFrC,IAyFE,MAAM;AAzFR,IA2FE,YAAY,KAAK;AA3FnB,IA4FE,UAAU,KAAK;AA5FjB,IA8FE,WAAW;AA9Fb,IA+FE,QAAQ;AA/FV,IAgGE,UAAU;AAhGZ,IAiGE,YAAY;AAjGd,IAmGE,OAAO;AAnGT,IAoGE,WAAW;AApGb,IAqGE,mBAAmB;AArGrB,IAuGE,iBAAiB,KAAK,SAAS;AAvGjC,IAwGE,eAAe,GAAG,SAAS;AAxG7B,IA2GE,IAAI,EAAE,aAAa,IAAI;AA0EzB,EAAE,gBAAgB,EAAE,MAAM,WAAY;AACpC,MAAI,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,MAAI,EAAE,IAAI;AAAG,MAAE,IAAI;AACnB,SAAO,SAAS,CAAC;AACnB;AAQA,EAAE,OAAO,WAAY;AACnB,SAAO,SAAS,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC;AAC3D;AAWA,EAAE,YAAY,EAAE,QAAQ,SAAUA,MAAKC,MAAK;AAC1C,MAAI,GACF,IAAI,MACJ,OAAO,EAAE;AACX,EAAAD,OAAM,IAAI,KAAKA,IAAG;AAClB,EAAAC,OAAM,IAAI,KAAKA,IAAG;AAClB,MAAI,CAACD,KAAI,KAAK,CAACC,KAAI;AAAG,WAAO,IAAI,KAAK,GAAG;AACzC,MAAID,KAAI,GAAGC,IAAG;AAAG,UAAM,MAAM,kBAAkBA,IAAG;AAClD,MAAI,EAAE,IAAID,IAAG;AACb,SAAO,IAAI,IAAIA,OAAM,EAAE,IAAIC,IAAG,IAAI,IAAIA,OAAM,IAAI,KAAK,CAAC;AACxD;AAWA,EAAE,aAAa,EAAE,MAAM,SAAU,GAAG;AAClC,MAAI,GAAG,GAAG,KAAK,KACb,IAAI,MACJ,KAAK,EAAE,GACP,MAAM,IAAI,IAAI,EAAE,YAAY,CAAC,GAAG,GAChC,KAAK,EAAE,GACP,KAAK,EAAE;AAGT,MAAI,CAAC,MAAM,CAAC,IAAI;AACd,WAAO,CAAC,MAAM,CAAC,KAAK,MAAM,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI;AAAA,EAChF;AAGA,MAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC;AAAG,WAAO,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;AAGxD,MAAI,OAAO;AAAI,WAAO;AAGtB,MAAI,EAAE,MAAM,EAAE;AAAG,WAAO,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI;AAEjD,QAAM,GAAG;AACT,QAAM,GAAG;AAGT,OAAK,IAAI,GAAG,IAAI,MAAM,MAAM,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG;AACjD,QAAI,GAAG,CAAC,MAAM,GAAG,CAAC;AAAG,aAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI;AAAA,EAC3D;AAGA,SAAO,QAAQ,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI,IAAI;AACpD;AAgBA,EAAE,SAAS,EAAE,MAAM,WAAY;AAC7B,MAAI,IAAI,IACN,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE;AAAG,WAAO,IAAI,KAAK,GAAG;AAG7B,MAAI,CAAC,EAAE,EAAE,CAAC;AAAG,WAAO,IAAI,KAAK,CAAC;AAE9B,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI;AAC9C,OAAK,WAAW;AAEhB,MAAI,OAAO,MAAM,iBAAiB,MAAM,CAAC,CAAC;AAE1C,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,SAAS,YAAY,KAAK,YAAY,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI;AAC5E;AAmBA,EAAE,WAAW,EAAE,OAAO,WAAY;AAChC,MAAI,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,SACjC,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE,SAAS,KAAK,EAAE,OAAO;AAAG,WAAO,IAAI,KAAK,CAAC;AAClD,aAAW;AAGX,MAAI,EAAE,IAAI,QAAQ,EAAE,IAAI,GAAG,IAAI,CAAC;AAIhC,MAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG;AAC9B,QAAI,eAAe,EAAE,CAAC;AACtB,QAAI,EAAE;AAGN,QAAI,KAAK,IAAI,EAAE,SAAS,KAAK;AAAG,WAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChE,QAAI,QAAQ,GAAG,IAAI,CAAC;AAGpB,QAAI,WAAW,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAErD,QAAI,KAAK,IAAI,GAAG;AACd,UAAI,OAAO;AAAA,IACb,OAAO;AACL,UAAI,EAAE,cAAc;AACpB,UAAI,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI;AAAA,IACvC;AAEA,QAAI,IAAI,KAAK,CAAC;AACd,MAAE,IAAI,EAAE;AAAA,EACV,OAAO;AACL,QAAI,IAAI,KAAK,EAAE,SAAS,CAAC;AAAA,EAC3B;AAEA,QAAM,IAAI,KAAK,aAAa;AAI5B,aAAS;AACP,QAAI;AACJ,SAAK,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;AACvB,cAAU,GAAG,KAAK,CAAC;AACnB,QAAI,OAAO,QAAQ,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,QAAQ,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC;AAGhE,QAAI,eAAe,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,OAAO,IAAI,eAAe,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG;AAC/E,UAAI,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC;AAI1B,UAAI,KAAK,UAAU,CAAC,OAAO,KAAK,QAAQ;AAItC,YAAI,CAAC,KAAK;AACR,mBAAS,GAAG,IAAI,GAAG,CAAC;AAEpB,cAAI,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG;AAC7B,gBAAI;AACJ;AAAA,UACF;AAAA,QACF;AAEA,cAAM;AACN,cAAM;AAAA,MACR,OAAO;AAIL,YAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,KAAK;AAG7C,mBAAS,GAAG,IAAI,GAAG,CAAC;AACpB,cAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC;AAAA,QAC/B;AAEA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,aAAW;AAEX,SAAO,SAAS,GAAG,GAAG,KAAK,UAAU,CAAC;AACxC;AAOA,EAAE,gBAAgB,EAAE,KAAK,WAAY;AACnC,MAAI,GACF,IAAI,KAAK,GACT,IAAI;AAEN,MAAI,GAAG;AACL,QAAI,EAAE,SAAS;AACf,SAAK,IAAI,UAAU,KAAK,IAAI,QAAQ,KAAK;AAGzC,QAAI,EAAE,CAAC;AACP,QAAI;AAAG,aAAO,IAAI,MAAM,GAAG,KAAK;AAAI;AACpC,QAAI,IAAI;AAAG,UAAI;AAAA,EACjB;AAEA,SAAO;AACT;AAwBA,EAAE,YAAY,EAAE,MAAM,SAAU,GAAG;AACjC,SAAO,OAAO,MAAM,IAAI,KAAK,YAAY,CAAC,CAAC;AAC7C;AAQA,EAAE,qBAAqB,EAAE,WAAW,SAAU,GAAG;AAC/C,MAAI,IAAI,MACN,OAAO,EAAE;AACX,SAAO,SAAS,OAAO,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,WAAW,KAAK,QAAQ;AAChF;AAOA,EAAE,SAAS,EAAE,KAAK,SAAU,GAAG;AAC7B,SAAO,KAAK,IAAI,CAAC,MAAM;AACzB;AAQA,EAAE,QAAQ,WAAY;AACpB,SAAO,SAAS,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC;AAC3D;AAQA,EAAE,cAAc,EAAE,KAAK,SAAU,GAAG;AAClC,SAAO,KAAK,IAAI,CAAC,IAAI;AACvB;AAQA,EAAE,uBAAuB,EAAE,MAAM,SAAU,GAAG;AAC5C,MAAI,IAAI,KAAK,IAAI,CAAC;AAClB,SAAO,KAAK,KAAK,MAAM;AACzB;AA4BA,EAAE,mBAAmB,EAAE,OAAO,WAAY;AACxC,MAAI,GAAG,GAAG,IAAI,IAAI,KAChB,IAAI,MACJ,OAAO,EAAE,aACT,MAAM,IAAI,KAAK,CAAC;AAElB,MAAI,CAAC,EAAE,SAAS;AAAG,WAAO,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,GAAG;AACpD,MAAI,EAAE,OAAO;AAAG,WAAO;AAEvB,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI;AAC9C,OAAK,WAAW;AAChB,QAAM,EAAE,EAAE;AAOV,MAAI,MAAM,IAAI;AACZ,QAAI,KAAK,KAAK,MAAM,CAAC;AACrB,SAAK,IAAI,QAAQ,GAAG,CAAC,GAAG,SAAS;AAAA,EACnC,OAAO;AACL,QAAI;AACJ,QAAI;AAAA,EACN;AAEA,MAAI,aAAa,MAAM,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI;AAGvD,MAAI,SACF,IAAI,GACJ,KAAK,IAAI,KAAK,CAAC;AACjB,SAAO,OAAM;AACX,cAAU,EAAE,MAAM,CAAC;AACnB,QAAI,IAAI,MAAM,QAAQ,MAAM,GAAG,MAAM,QAAQ,MAAM,EAAE,CAAC,CAAC,CAAC;AAAA,EAC1D;AAEA,SAAO,SAAS,GAAG,KAAK,YAAY,IAAI,KAAK,WAAW,IAAI,IAAI;AAClE;AAiCA,EAAE,iBAAiB,EAAE,OAAO,WAAY;AACtC,MAAI,GAAG,IAAI,IAAI,KACb,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE,SAAS,KAAK,EAAE,OAAO;AAAG,WAAO,IAAI,KAAK,CAAC;AAElD,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI;AAC9C,OAAK,WAAW;AAChB,QAAM,EAAE,EAAE;AAEV,MAAI,MAAM,GAAG;AACX,QAAI,aAAa,MAAM,GAAG,GAAG,GAAG,IAAI;AAAA,EACtC,OAAO;AAWL,QAAI,MAAM,KAAK,KAAK,GAAG;AACvB,QAAI,IAAI,KAAK,KAAK,IAAI;AAEtB,QAAI,EAAE,MAAM,IAAI,QAAQ,GAAG,CAAC,CAAC;AAC7B,QAAI,aAAa,MAAM,GAAG,GAAG,GAAG,IAAI;AAGpC,QAAI,SACF,KAAK,IAAI,KAAK,CAAC,GACf,MAAM,IAAI,KAAK,EAAE,GACjB,MAAM,IAAI,KAAK,EAAE;AACnB,WAAO,OAAM;AACX,gBAAU,EAAE,MAAM,CAAC;AACnB,UAAI,EAAE,MAAM,GAAG,KAAK,QAAQ,MAAM,IAAI,MAAM,OAAO,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,IAClE;AAAA,EACF;AAEA,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,SAAS,GAAG,IAAI,IAAI,IAAI;AACjC;AAmBA,EAAE,oBAAoB,EAAE,OAAO,WAAY;AACzC,MAAI,IAAI,IACN,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE,SAAS;AAAG,WAAO,IAAI,KAAK,EAAE,CAAC;AACtC,MAAI,EAAE,OAAO;AAAG,WAAO,IAAI,KAAK,CAAC;AAEjC,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK;AACtB,OAAK,WAAW;AAEhB,SAAO,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,KAAK,YAAY,IAAI,KAAK,WAAW,EAAE;AAC3E;AAsBA,EAAE,gBAAgB,EAAE,OAAO,WAAY;AACrC,MAAI,IAAI,MACN,OAAO,EAAE,aACT,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GACjB,KAAK,KAAK,WACV,KAAK,KAAK;AAEZ,MAAI,MAAM,IAAI;AACZ,WAAO,MAAM,IAET,EAAE,MAAM,IAAI,MAAM,MAAM,IAAI,EAAE,IAAI,IAAI,KAAK,CAAC,IAE5C,IAAI,KAAK,GAAG;AAAA,EAClB;AAEA,MAAI,EAAE,OAAO;AAAG,WAAO,MAAM,MAAM,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG;AAIxD,OAAK,YAAY,KAAK;AACtB,OAAK,WAAW;AAGhB,MAAI,IAAI,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK;AAEpD,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,EAAE,MAAM,CAAC;AAClB;AAsBA,EAAE,0BAA0B,EAAE,QAAQ,WAAY;AAChD,MAAI,IAAI,IACN,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,EAAE,IAAI,CAAC;AAAG,WAAO,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG;AAC/C,MAAI,CAAC,EAAE,SAAS;AAAG,WAAO,IAAI,KAAK,CAAC;AAEpC,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI;AACxD,OAAK,WAAW;AAChB,aAAW;AAEX,MAAI,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAErC,aAAW;AACX,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,EAAE,GAAG;AACd;AAmBA,EAAE,wBAAwB,EAAE,QAAQ,WAAY;AAC9C,MAAI,IAAI,IACN,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE,SAAS,KAAK,EAAE,OAAO;AAAG,WAAO,IAAI,KAAK,CAAC;AAElD,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI;AAC5D,OAAK,WAAW;AAChB,aAAW;AAEX,MAAI,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAEpC,aAAW;AACX,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,EAAE,GAAG;AACd;AAsBA,EAAE,2BAA2B,EAAE,QAAQ,WAAY;AACjD,MAAI,IAAI,IAAI,KAAK,KACf,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE,SAAS;AAAG,WAAO,IAAI,KAAK,GAAG;AACtC,MAAI,EAAE,KAAK;AAAG,WAAO,IAAI,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,GAAG;AAE5E,OAAK,KAAK;AACV,OAAK,KAAK;AACV,QAAM,EAAE,GAAG;AAEX,MAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI;AAAG,WAAO,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI;AAE/E,OAAK,YAAY,MAAM,MAAM,EAAE;AAE/B,MAAI,OAAO,EAAE,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC;AAEvD,OAAK,YAAY,KAAK;AACtB,OAAK,WAAW;AAEhB,MAAI,EAAE,GAAG;AAET,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,EAAE,MAAM,GAAG;AACpB;AAwBA,EAAE,cAAc,EAAE,OAAO,WAAY;AACnC,MAAI,QAAQ,GACV,IAAI,IACJ,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,EAAE,OAAO;AAAG,WAAO,IAAI,KAAK,CAAC;AAEjC,MAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACjB,OAAK,KAAK;AACV,OAAK,KAAK;AAEV,MAAI,MAAM,IAAI;AAGZ,QAAI,MAAM,GAAG;AACX,eAAS,MAAM,MAAM,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG;AAC1C,aAAO,IAAI,EAAE;AACb,aAAO;AAAA,IACT;AAGA,WAAO,IAAI,KAAK,GAAG;AAAA,EACrB;AAIA,OAAK,YAAY,KAAK;AACtB,OAAK,WAAW;AAEhB,MAAI,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK;AAE7D,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,EAAE,MAAM,CAAC;AAClB;AAqBA,EAAE,iBAAiB,EAAE,OAAO,WAAY;AACtC,MAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,IAC7B,IAAI,MACJ,OAAO,EAAE,aACT,KAAK,KAAK,WACV,KAAK,KAAK;AAEZ,MAAI,CAAC,EAAE,SAAS,GAAG;AACjB,QAAI,CAAC,EAAE;AAAG,aAAO,IAAI,KAAK,GAAG;AAC7B,QAAI,KAAK,KAAK,cAAc;AAC1B,UAAI,MAAM,MAAM,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG;AACrC,QAAE,IAAI,EAAE;AACR,aAAO;AAAA,IACT;AAAA,EACF,WAAW,EAAE,OAAO,GAAG;AACrB,WAAO,IAAI,KAAK,CAAC;AAAA,EACnB,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,KAAK,KAAK,cAAc;AAClD,QAAI,MAAM,MAAM,KAAK,GAAG,EAAE,EAAE,MAAM,IAAI;AACtC,MAAE,IAAI,EAAE;AACR,WAAO;AAAA,EACT;AAEA,OAAK,YAAY,MAAM,KAAK;AAC5B,OAAK,WAAW;AAQhB,MAAI,KAAK,IAAI,IAAI,MAAM,WAAW,IAAI,CAAC;AAEvC,OAAK,IAAI,GAAG,GAAG,EAAE;AAAG,QAAI,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAE/D,aAAW;AAEX,MAAI,KAAK,KAAK,MAAM,QAAQ;AAC5B,MAAI;AACJ,OAAK,EAAE,MAAM,CAAC;AACd,MAAI,IAAI,KAAK,CAAC;AACd,OAAK;AAGL,SAAO,MAAM,MAAK;AAChB,SAAK,GAAG,MAAM,EAAE;AAChB,QAAI,EAAE,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC;AAE1B,SAAK,GAAG,MAAM,EAAE;AAChB,QAAI,EAAE,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC;AAEzB,QAAI,EAAE,EAAE,CAAC,MAAM;AAAQ,WAAK,IAAI,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK;AAAK;AAAA,EAC/D;AAEA,MAAI;AAAG,QAAI,EAAE,MAAM,KAAM,IAAI,CAAE;AAE/B,aAAW;AAEX,SAAO,SAAS,GAAG,KAAK,YAAY,IAAI,KAAK,WAAW,IAAI,IAAI;AAClE;AAOA,EAAE,WAAW,WAAY;AACvB,SAAO,CAAC,CAAC,KAAK;AAChB;AAOA,EAAE,YAAY,EAAE,QAAQ,WAAY;AAClC,SAAO,CAAC,CAAC,KAAK,KAAK,UAAU,KAAK,IAAI,QAAQ,IAAI,KAAK,EAAE,SAAS;AACpE;AAOA,EAAE,QAAQ,WAAY;AACpB,SAAO,CAAC,KAAK;AACf;AAOA,EAAE,aAAa,EAAE,QAAQ,WAAY;AACnC,SAAO,KAAK,IAAI;AAClB;AAOA,EAAE,aAAa,EAAE,QAAQ,WAAY;AACnC,SAAO,KAAK,IAAI;AAClB;AAOA,EAAE,SAAS,WAAY;AACrB,SAAO,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC,MAAM;AACnC;AAOA,EAAE,WAAW,EAAE,KAAK,SAAU,GAAG;AAC/B,SAAO,KAAK,IAAI,CAAC,IAAI;AACvB;AAOA,EAAE,oBAAoB,EAAE,MAAM,SAAU,GAAG;AACzC,SAAO,KAAK,IAAI,CAAC,IAAI;AACvB;AAiCA,EAAE,YAAY,EAAE,MAAM,SAAU,MAAM;AACpC,MAAI,UAAU,GAAG,aAAa,GAAG,KAAK,KAAK,IAAI,GAC7C,MAAM,MACN,OAAO,IAAI,aACX,KAAK,KAAK,WACV,KAAK,KAAK,UACV,QAAQ;AAGV,MAAI,QAAQ,MAAM;AAChB,WAAO,IAAI,KAAK,EAAE;AAClB,eAAW;AAAA,EACb,OAAO;AACL,WAAO,IAAI,KAAK,IAAI;AACpB,QAAI,KAAK;AAGT,QAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,GAAG,CAAC;AAAG,aAAO,IAAI,KAAK,GAAG;AAEhE,eAAW,KAAK,GAAG,EAAE;AAAA,EACvB;AAEA,MAAI,IAAI;AAGR,MAAI,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG;AACzC,WAAO,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC;AAAA,EACxE;AAIA,MAAI,UAAU;AACZ,QAAI,EAAE,SAAS,GAAG;AAChB,YAAM;AAAA,IACR,OAAO;AACL,WAAK,IAAI,EAAE,CAAC,GAAG,IAAI,OAAO;AAAI,aAAK;AACnC,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AAEA,aAAW;AACX,OAAK,KAAK;AACV,QAAM,iBAAiB,KAAK,EAAE;AAC9B,gBAAc,WAAW,QAAQ,MAAM,KAAK,EAAE,IAAI,iBAAiB,MAAM,EAAE;AAG3E,MAAI,OAAO,KAAK,aAAa,IAAI,CAAC;AAgBlC,MAAI,oBAAoB,EAAE,GAAG,IAAI,IAAI,EAAE,GAAG;AAExC,OAAG;AACD,YAAM;AACN,YAAM,iBAAiB,KAAK,EAAE;AAC9B,oBAAc,WAAW,QAAQ,MAAM,KAAK,EAAE,IAAI,iBAAiB,MAAM,EAAE;AAC3E,UAAI,OAAO,KAAK,aAAa,IAAI,CAAC;AAElC,UAAI,CAAC,KAAK;AAGR,YAAI,CAAC,eAAe,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,IAAI,EAAE,IAAI,KAAK,MAAM;AACzD,cAAI,SAAS,GAAG,KAAK,GAAG,CAAC;AAAA,QAC3B;AAEA;AAAA,MACF;AAAA,IACF,SAAS,oBAAoB,EAAE,GAAG,KAAK,IAAI,EAAE;AAAA,EAC/C;AAEA,aAAW;AAEX,SAAO,SAAS,GAAG,IAAI,EAAE;AAC3B;AAgDA,EAAE,QAAQ,EAAE,MAAM,SAAU,GAAG;AAC7B,MAAI,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,MAAM,IAC5C,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,IAAI,KAAK,CAAC;AAGd,MAAI,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG;AAGhB,QAAI,CAAC,EAAE,KAAK,CAAC,EAAE;AAAG,UAAI,IAAI,KAAK,GAAG;AAAA,aAGzB,EAAE;AAAG,QAAE,IAAI,CAAC,EAAE;AAAA;AAKlB,UAAI,IAAI,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,GAAG;AAE9C,WAAO;AAAA,EACT;AAGA,MAAI,EAAE,KAAK,EAAE,GAAG;AACd,MAAE,IAAI,CAAC,EAAE;AACT,WAAO,EAAE,KAAK,CAAC;AAAA,EACjB;AAEA,OAAK,EAAE;AACP,OAAK,EAAE;AACP,OAAK,KAAK;AACV,OAAK,KAAK;AAGV,MAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AAGpB,QAAI,GAAG,CAAC;AAAG,QAAE,IAAI,CAAC,EAAE;AAAA,aAGX,GAAG,CAAC;AAAG,UAAI,IAAI,KAAK,CAAC;AAAA;AAIzB,aAAO,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC;AAEtC,WAAO,WAAW,SAAS,GAAG,IAAI,EAAE,IAAI;AAAA,EAC1C;AAKA,MAAI,UAAU,EAAE,IAAI,QAAQ;AAC5B,OAAK,UAAU,EAAE,IAAI,QAAQ;AAE7B,OAAK,GAAG,MAAM;AACd,MAAI,KAAK;AAGT,MAAI,GAAG;AACL,WAAO,IAAI;AAEX,QAAI,MAAM;AACR,UAAI;AACJ,UAAI,CAAC;AACL,YAAM,GAAG;AAAA,IACX,OAAO;AACL,UAAI;AACJ,UAAI;AACJ,YAAM,GAAG;AAAA,IACX;AAKA,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,QAAQ,GAAG,GAAG,IAAI;AAE9C,QAAI,IAAI,GAAG;AACT,UAAI;AACJ,QAAE,SAAS;AAAA,IACb;AAGA,MAAE,QAAQ;AACV,SAAK,IAAI,GAAG;AAAM,QAAE,KAAK,CAAC;AAC1B,MAAE,QAAQ;AAAA,EAGZ,OAAO;AAIL,QAAI,GAAG;AACP,UAAM,GAAG;AACT,WAAO,IAAI;AACX,QAAI;AAAM,YAAM;AAEhB,SAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,UAAI,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG;AAClB,eAAO,GAAG,CAAC,IAAI,GAAG,CAAC;AACnB;AAAA,MACF;AAAA,IACF;AAEA,QAAI;AAAA,EACN;AAEA,MAAI,MAAM;AACR,QAAI;AACJ,SAAK;AACL,SAAK;AACL,MAAE,IAAI,CAAC,EAAE;AAAA,EACX;AAEA,QAAM,GAAG;AAIT,OAAK,IAAI,GAAG,SAAS,KAAK,IAAI,GAAG,EAAE;AAAG,OAAG,KAAK,IAAI;AAGlD,OAAK,IAAI,GAAG,QAAQ,IAAI,KAAI;AAE1B,QAAI,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG;AACnB,WAAK,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC,MAAM;AAAI,WAAG,CAAC,IAAI,OAAO;AAChD,QAAE,GAAG,CAAC;AACN,SAAG,CAAC,KAAK;AAAA,IACX;AAEA,OAAG,CAAC,KAAK,GAAG,CAAC;AAAA,EACf;AAGA,SAAO,GAAG,EAAE,GAAG,MAAM;AAAI,OAAG,IAAI;AAGhC,SAAO,GAAG,CAAC,MAAM,GAAG,GAAG,MAAM;AAAG,MAAE;AAGlC,MAAI,CAAC,GAAG,CAAC;AAAG,WAAO,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC;AAE7C,IAAE,IAAI;AACN,IAAE,IAAI,kBAAkB,IAAI,CAAC;AAE7B,SAAO,WAAW,SAAS,GAAG,IAAI,EAAE,IAAI;AAC1C;AA2BA,EAAE,SAAS,EAAE,MAAM,SAAU,GAAG;AAC9B,MAAI,GACF,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,IAAI,KAAK,CAAC;AAGd,MAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC;AAAG,WAAO,IAAI,KAAK,GAAG;AAGvD,MAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG;AAC1B,WAAO,SAAS,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,KAAK,QAAQ;AAAA,EAC5D;AAGA,aAAW;AAEX,MAAI,KAAK,UAAU,GAAG;AAIpB,QAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC;AAC9B,MAAE,KAAK,EAAE;AAAA,EACX,OAAO;AACL,QAAI,OAAO,GAAG,GAAG,GAAG,KAAK,QAAQ,CAAC;AAAA,EACpC;AAEA,MAAI,EAAE,MAAM,CAAC;AAEb,aAAW;AAEX,SAAO,EAAE,MAAM,CAAC;AAClB;AASA,EAAE,qBAAqB,EAAE,MAAM,WAAY;AACzC,SAAO,mBAAmB,IAAI;AAChC;AAQA,EAAE,mBAAmB,EAAE,KAAK,WAAY;AACtC,SAAO,iBAAiB,IAAI;AAC9B;AAQA,EAAE,UAAU,EAAE,MAAM,WAAY;AAC9B,MAAI,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,IAAE,IAAI,CAAC,EAAE;AACT,SAAO,SAAS,CAAC;AACnB;AAwBA,EAAE,OAAO,EAAE,MAAM,SAAU,GAAG;AAC5B,MAAI,OAAO,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,IAAI,IACtC,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,IAAI,KAAK,CAAC;AAGd,MAAI,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG;AAGhB,QAAI,CAAC,EAAE,KAAK,CAAC,EAAE;AAAG,UAAI,IAAI,KAAK,GAAG;AAAA,aAMzB,CAAC,EAAE;AAAG,UAAI,IAAI,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,GAAG;AAExD,WAAO;AAAA,EACT;AAGA,MAAI,EAAE,KAAK,EAAE,GAAG;AACd,MAAE,IAAI,CAAC,EAAE;AACT,WAAO,EAAE,MAAM,CAAC;AAAA,EAClB;AAEA,OAAK,EAAE;AACP,OAAK,EAAE;AACP,OAAK,KAAK;AACV,OAAK,KAAK;AAGV,MAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AAIpB,QAAI,CAAC,GAAG,CAAC;AAAG,UAAI,IAAI,KAAK,CAAC;AAE1B,WAAO,WAAW,SAAS,GAAG,IAAI,EAAE,IAAI;AAAA,EAC1C;AAKA,MAAI,UAAU,EAAE,IAAI,QAAQ;AAC5B,MAAI,UAAU,EAAE,IAAI,QAAQ;AAE5B,OAAK,GAAG,MAAM;AACd,MAAI,IAAI;AAGR,MAAI,GAAG;AAEL,QAAI,IAAI,GAAG;AACT,UAAI;AACJ,UAAI,CAAC;AACL,YAAM,GAAG;AAAA,IACX,OAAO;AACL,UAAI;AACJ,UAAI;AACJ,YAAM,GAAG;AAAA,IACX;AAGA,QAAI,KAAK,KAAK,KAAK,QAAQ;AAC3B,UAAM,IAAI,MAAM,IAAI,IAAI,MAAM;AAE9B,QAAI,IAAI,KAAK;AACX,UAAI;AACJ,QAAE,SAAS;AAAA,IACb;AAGA,MAAE,QAAQ;AACV,WAAO;AAAM,QAAE,KAAK,CAAC;AACrB,MAAE,QAAQ;AAAA,EACZ;AAEA,QAAM,GAAG;AACT,MAAI,GAAG;AAGP,MAAI,MAAM,IAAI,GAAG;AACf,QAAI;AACJ,QAAI;AACJ,SAAK;AACL,SAAK;AAAA,EACP;AAGA,OAAK,QAAQ,GAAG,KAAI;AAClB,aAAS,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,SAAS,OAAO;AACnD,OAAG,CAAC,KAAK;AAAA,EACX;AAEA,MAAI,OAAO;AACT,OAAG,QAAQ,KAAK;AAChB,MAAE;AAAA,EACJ;AAIA,OAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,GAAG,KAAK;AAAI,OAAG,IAAI;AAE9C,IAAE,IAAI;AACN,IAAE,IAAI,kBAAkB,IAAI,CAAC;AAE7B,SAAO,WAAW,SAAS,GAAG,IAAI,EAAE,IAAI;AAC1C;AASA,EAAE,YAAY,EAAE,KAAK,SAAU,GAAG;AAChC,MAAI,GACF,IAAI;AAEN,MAAI,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,MAAM,KAAK,MAAM;AAAG,UAAM,MAAM,kBAAkB,CAAC;AAEpF,MAAI,EAAE,GAAG;AACP,QAAI,aAAa,EAAE,CAAC;AACpB,QAAI,KAAK,EAAE,IAAI,IAAI;AAAG,UAAI,EAAE,IAAI;AAAA,EAClC,OAAO;AACL,QAAI;AAAA,EACN;AAEA,SAAO;AACT;AAQA,EAAE,QAAQ,WAAY;AACpB,MAAI,IAAI,MACN,OAAO,EAAE;AAEX,SAAO,SAAS,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,KAAK,QAAQ;AACrD;AAkBA,EAAE,OAAO,EAAE,MAAM,WAAY;AAC3B,MAAI,IAAI,IACN,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE,SAAS;AAAG,WAAO,IAAI,KAAK,GAAG;AACtC,MAAI,EAAE,OAAO;AAAG,WAAO,IAAI,KAAK,CAAC;AAEjC,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI;AAC9C,OAAK,WAAW;AAEhB,MAAI,KAAK,MAAM,iBAAiB,MAAM,CAAC,CAAC;AAExC,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,SAAS,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI;AAC1D;AAeA,EAAE,aAAa,EAAE,OAAO,WAAY;AAClC,MAAI,GAAG,GAAG,IAAI,GAAG,KAAK,GACpB,IAAI,MACJ,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,OAAO,EAAE;AAGX,MAAI,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;AAC1B,WAAO,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC;AAAA,EACnE;AAEA,aAAW;AAGX,MAAI,KAAK,KAAK,CAAC,CAAC;AAIhB,MAAI,KAAK,KAAK,KAAK,IAAI,GAAG;AACxB,QAAI,eAAe,CAAC;AAEpB,SAAK,EAAE,SAAS,KAAK,KAAK;AAAG,WAAK;AAClC,QAAI,KAAK,KAAK,CAAC;AACf,QAAI,WAAW,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,IAAI;AAE3C,QAAI,KAAK,IAAI,GAAG;AACd,UAAI,OAAO;AAAA,IACb,OAAO;AACL,UAAI,EAAE,cAAc;AACpB,UAAI,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI;AAAA,IACvC;AAEA,QAAI,IAAI,KAAK,CAAC;AAAA,EAChB,OAAO;AACL,QAAI,IAAI,KAAK,EAAE,SAAS,CAAC;AAAA,EAC3B;AAEA,QAAM,IAAI,KAAK,aAAa;AAG5B,aAAS;AACP,QAAI;AACJ,QAAI,EAAE,KAAK,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG;AAG7C,QAAI,eAAe,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,OAAO,IAAI,eAAe,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG;AAC/E,UAAI,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC;AAI1B,UAAI,KAAK,UAAU,CAAC,OAAO,KAAK,QAAQ;AAItC,YAAI,CAAC,KAAK;AACR,mBAAS,GAAG,IAAI,GAAG,CAAC;AAEpB,cAAI,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG;AACpB,gBAAI;AACJ;AAAA,UACF;AAAA,QACF;AAEA,cAAM;AACN,cAAM;AAAA,MACR,OAAO;AAIL,YAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,KAAK;AAG7C,mBAAS,GAAG,IAAI,GAAG,CAAC;AACpB,cAAI,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC;AAAA,QACtB;AAEA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,aAAW;AAEX,SAAO,SAAS,GAAG,GAAG,KAAK,UAAU,CAAC;AACxC;AAgBA,EAAE,UAAU,EAAE,MAAM,WAAY;AAC9B,MAAI,IAAI,IACN,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,CAAC,EAAE,SAAS;AAAG,WAAO,IAAI,KAAK,GAAG;AACtC,MAAI,EAAE,OAAO;AAAG,WAAO,IAAI,KAAK,CAAC;AAEjC,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,YAAY,KAAK;AACtB,OAAK,WAAW;AAEhB,MAAI,EAAE,IAAI;AACV,IAAE,IAAI;AACN,MAAI,OAAO,GAAG,IAAI,KAAK,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,IAAI,CAAC;AAE9D,OAAK,YAAY;AACjB,OAAK,WAAW;AAEhB,SAAO,SAAS,YAAY,KAAK,YAAY,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI;AAC5E;AAwBA,EAAE,QAAQ,EAAE,MAAM,SAAU,GAAG;AAC7B,MAAI,OAAO,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,KACjC,IAAI,MACJ,OAAO,EAAE,aACT,KAAK,EAAE,GACP,MAAM,IAAI,IAAI,KAAK,CAAC,GAAG;AAEzB,IAAE,KAAK,EAAE;AAGT,MAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;AAElC,WAAO,IAAI,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAI5D,MAIA,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC;AAAA,EACpC;AAEA,MAAI,UAAU,EAAE,IAAI,QAAQ,IAAI,UAAU,EAAE,IAAI,QAAQ;AACxD,QAAM,GAAG;AACT,QAAM,GAAG;AAGT,MAAI,MAAM,KAAK;AACb,QAAI;AACJ,SAAK;AACL,SAAK;AACL,SAAK;AACL,UAAM;AACN,UAAM;AAAA,EACR;AAGA,MAAI,CAAC;AACL,OAAK,MAAM;AACX,OAAK,IAAI,IAAI;AAAM,MAAE,KAAK,CAAC;AAG3B,OAAK,IAAI,KAAK,EAAE,KAAK,KAAI;AACvB,YAAQ;AACR,SAAK,IAAI,MAAM,GAAG,IAAI,KAAI;AACxB,UAAI,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI;AACnC,QAAE,GAAG,IAAI,IAAI,OAAO;AACpB,cAAQ,IAAI,OAAO;AAAA,IACrB;AAEA,MAAE,CAAC,KAAK,EAAE,CAAC,IAAI,SAAS,OAAO;AAAA,EACjC;AAGA,SAAO,CAAC,EAAE,EAAE,EAAE;AAAI,MAAE,IAAI;AAExB,MAAI;AAAO,MAAE;AAAA;AACR,MAAE,MAAM;AAEb,IAAE,IAAI;AACN,IAAE,IAAI,kBAAkB,GAAG,CAAC;AAE5B,SAAO,WAAW,SAAS,GAAG,KAAK,WAAW,KAAK,QAAQ,IAAI;AACjE;AAaA,EAAE,WAAW,SAAU,IAAI,IAAI;AAC7B,SAAO,eAAe,MAAM,GAAG,IAAI,EAAE;AACvC;AAaA,EAAE,kBAAkB,EAAE,OAAO,SAAU,IAAI,IAAI;AAC7C,MAAI,IAAI,MACN,OAAO,EAAE;AAEX,MAAI,IAAI,KAAK,CAAC;AACd,MAAI,OAAO;AAAQ,WAAO;AAE1B,aAAW,IAAI,GAAG,UAAU;AAE5B,MAAI,OAAO;AAAQ,SAAK,KAAK;AAAA;AACxB,eAAW,IAAI,GAAG,CAAC;AAExB,SAAO,SAAS,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE;AACrC;AAWA,EAAE,gBAAgB,SAAU,IAAI,IAAI;AAClC,MAAI,KACF,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,OAAO,QAAQ;AACjB,UAAM,eAAe,GAAG,IAAI;AAAA,EAC9B,OAAO;AACL,eAAW,IAAI,GAAG,UAAU;AAE5B,QAAI,OAAO;AAAQ,WAAK,KAAK;AAAA;AACxB,iBAAW,IAAI,GAAG,CAAC;AAExB,QAAI,SAAS,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;AACpC,UAAM,eAAe,GAAG,MAAM,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO,EAAE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,MAAM,MAAM;AAChD;AAmBA,EAAE,UAAU,SAAU,IAAI,IAAI;AAC5B,MAAI,KAAK,GACP,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,OAAO,QAAQ;AACjB,UAAM,eAAe,CAAC;AAAA,EACxB,OAAO;AACL,eAAW,IAAI,GAAG,UAAU;AAE5B,QAAI,OAAO;AAAQ,WAAK,KAAK;AAAA;AACxB,iBAAW,IAAI,GAAG,CAAC;AAExB,QAAI,SAAS,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE;AAC1C,UAAM,eAAe,GAAG,OAAO,KAAK,EAAE,IAAI,CAAC;AAAA,EAC7C;AAIA,SAAO,EAAE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,MAAM,MAAM;AAChD;AAcA,EAAE,aAAa,SAAU,MAAM;AAC7B,MAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GACzC,IAAI,MACJ,KAAK,EAAE,GACP,OAAO,EAAE;AAEX,MAAI,CAAC;AAAI,WAAO,IAAI,KAAK,CAAC;AAE1B,OAAK,KAAK,IAAI,KAAK,CAAC;AACpB,OAAK,KAAK,IAAI,KAAK,CAAC;AAEpB,MAAI,IAAI,KAAK,EAAE;AACf,MAAI,EAAE,IAAI,aAAa,EAAE,IAAI,EAAE,IAAI;AACnC,MAAI,IAAI;AACR,IAAE,EAAE,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,WAAW,IAAI,CAAC;AAE7C,MAAI,QAAQ,MAAM;AAGhB,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB,OAAO;AACL,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,CAAC,EAAE,MAAM,KAAK,EAAE,GAAG,EAAE;AAAG,YAAM,MAAM,kBAAkB,CAAC;AAC3D,WAAO,EAAE,GAAG,CAAC,IAAK,IAAI,IAAI,IAAI,KAAM;AAAA,EACtC;AAEA,aAAW;AACX,MAAI,IAAI,KAAK,eAAe,EAAE,CAAC;AAC/B,OAAK,KAAK;AACV,OAAK,YAAY,IAAI,GAAG,SAAS,WAAW;AAE5C,aAAU;AACR,QAAI,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC;AACxB,SAAK,GAAG,KAAK,EAAE,MAAM,EAAE,CAAC;AACxB,QAAI,GAAG,IAAI,IAAI,KAAK;AAAG;AACvB,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK,GAAG,KAAK,EAAE,MAAM,EAAE,CAAC;AACxB,SAAK;AACL,SAAK;AACL,QAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AACvB,QAAI;AAAA,EACN;AAEA,OAAK,OAAO,KAAK,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC;AACvC,OAAK,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;AACzB,OAAK,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;AACzB,KAAG,IAAI,GAAG,IAAI,EAAE;AAGhB,MAAI,OAAO,IAAI,IAAI,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,OAAO,IAAI,IAAI,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,IAC7E,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;AAExB,OAAK,YAAY;AACjB,aAAW;AAEX,SAAO;AACT;AAaA,EAAE,gBAAgB,EAAE,QAAQ,SAAU,IAAI,IAAI;AAC5C,SAAO,eAAe,MAAM,IAAI,IAAI,EAAE;AACxC;AAmBA,EAAE,YAAY,SAAU,GAAG,IAAI;AAC7B,MAAI,IAAI,MACN,OAAO,EAAE;AAEX,MAAI,IAAI,KAAK,CAAC;AAEd,MAAI,KAAK,MAAM;AAGb,QAAI,CAAC,EAAE;AAAG,aAAO;AAEjB,QAAI,IAAI,KAAK,CAAC;AACd,SAAK,KAAK;AAAA,EACZ,OAAO;AACL,QAAI,IAAI,KAAK,CAAC;AACd,QAAI,OAAO,QAAQ;AACjB,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,iBAAW,IAAI,GAAG,CAAC;AAAA,IACrB;AAGA,QAAI,CAAC,EAAE;AAAG,aAAO,EAAE,IAAI,IAAI;AAG3B,QAAI,CAAC,EAAE,GAAG;AACR,UAAI,EAAE;AAAG,UAAE,IAAI,EAAE;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,EAAE,EAAE,CAAC,GAAG;AACV,eAAW;AACX,QAAI,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC;AAClC,eAAW;AACX,aAAS,CAAC;AAAA,EAGZ,OAAO;AACL,MAAE,IAAI,EAAE;AACR,QAAI;AAAA,EACN;AAEA,SAAO;AACT;AAQA,EAAE,WAAW,WAAY;AACvB,SAAO,CAAC;AACV;AAaA,EAAE,UAAU,SAAU,IAAI,IAAI;AAC5B,SAAO,eAAe,MAAM,GAAG,IAAI,EAAE;AACvC;AA8CA,EAAE,UAAU,EAAE,MAAM,SAAU,GAAG;AAC/B,MAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GACnB,IAAI,MACJ,OAAO,EAAE,aACT,KAAK,EAAE,IAAI,IAAI,KAAK,CAAC;AAGvB,MAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;AAAG,WAAO,IAAI,KAAK,QAAQ,CAAC,GAAG,EAAE,CAAC;AAEvE,MAAI,IAAI,KAAK,CAAC;AAEd,MAAI,EAAE,GAAG,CAAC;AAAG,WAAO;AAEpB,OAAK,KAAK;AACV,OAAK,KAAK;AAEV,MAAI,EAAE,GAAG,CAAC;AAAG,WAAO,SAAS,GAAG,IAAI,EAAE;AAGtC,MAAI,UAAU,EAAE,IAAI,QAAQ;AAG5B,MAAI,KAAK,EAAE,EAAE,SAAS,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,OAAO,kBAAkB;AACtE,QAAI,OAAO,MAAM,GAAG,GAAG,EAAE;AACzB,WAAO,EAAE,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,SAAS,GAAG,IAAI,EAAE;AAAA,EAC1D;AAEA,MAAI,EAAE;AAGN,MAAI,IAAI,GAAG;AAGT,QAAI,IAAI,EAAE,EAAE,SAAS;AAAG,aAAO,IAAI,KAAK,GAAG;AAG3C,SAAK,EAAE,EAAE,CAAC,IAAI,MAAM;AAAG,UAAI;AAG3B,QAAI,EAAE,KAAK,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,EAAE,EAAE,UAAU,GAAG;AAC9C,QAAE,IAAI;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAMA,MAAI,QAAQ,CAAC,GAAG,EAAE;AAClB,MAAI,KAAK,KAAK,CAAC,SAAS,CAAC,IACrB,UAAU,MAAM,KAAK,IAAI,OAAO,eAAe,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,IAC3E,IAAI,KAAK,IAAI,EAAE,EAAE;AAKrB,MAAI,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO;AAAG,WAAO,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC;AAE7E,aAAW;AACX,OAAK,WAAW,EAAE,IAAI;AAMtB,MAAI,KAAK,IAAI,KAAK,IAAI,IAAI,MAAM;AAGhC,MAAI,mBAAmB,EAAE,MAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE;AAG/D,MAAI,EAAE,GAAG;AAGP,QAAI,SAAS,GAAG,KAAK,GAAG,CAAC;AAIzB,QAAI,oBAAoB,EAAE,GAAG,IAAI,EAAE,GAAG;AACpC,UAAI,KAAK;AAGT,UAAI,SAAS,mBAAmB,EAAE,MAAM,iBAAiB,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;AAGjF,UAAI,CAAC,eAAe,EAAE,CAAC,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,MAAM;AAC3D,YAAI,SAAS,GAAG,KAAK,GAAG,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAEA,IAAE,IAAI;AACN,aAAW;AACX,OAAK,WAAW;AAEhB,SAAO,SAAS,GAAG,IAAI,EAAE;AAC3B;AAcA,EAAE,cAAc,SAAU,IAAI,IAAI;AAChC,MAAI,KACF,IAAI,MACJ,OAAO,EAAE;AAEX,MAAI,OAAO,QAAQ;AACjB,UAAM,eAAe,GAAG,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,KAAK,QAAQ;AAAA,EACtE,OAAO;AACL,eAAW,IAAI,GAAG,UAAU;AAE5B,QAAI,OAAO;AAAQ,WAAK,KAAK;AAAA;AACxB,iBAAW,IAAI,GAAG,CAAC;AAExB,QAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,EAAE;AAChC,UAAM,eAAe,GAAG,MAAM,EAAE,KAAK,EAAE,KAAK,KAAK,UAAU,EAAE;AAAA,EAC/D;AAEA,SAAO,EAAE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,MAAM,MAAM;AAChD;AAiBA,EAAE,sBAAsB,EAAE,OAAO,SAAU,IAAI,IAAI;AACjD,MAAI,IAAI,MACN,OAAO,EAAE;AAEX,MAAI,OAAO,QAAQ;AACjB,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACZ,OAAO;AACL,eAAW,IAAI,GAAG,UAAU;AAE5B,QAAI,OAAO;AAAQ,WAAK,KAAK;AAAA;AACxB,iBAAW,IAAI,GAAG,CAAC;AAAA,EAC1B;AAEA,SAAO,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,EAAE;AACrC;AAUA,EAAE,WAAW,WAAY;AACvB,MAAI,IAAI,MACN,OAAO,EAAE,aACT,MAAM,eAAe,GAAG,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,KAAK,QAAQ;AAEtE,SAAO,EAAE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,MAAM,MAAM;AAChD;AAOA,EAAE,YAAY,EAAE,QAAQ,WAAY;AAClC,SAAO,SAAS,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC;AAC3D;AAQA,EAAE,UAAU,EAAE,SAAS,WAAY;AACjC,MAAI,IAAI,MACN,OAAO,EAAE,aACT,MAAM,eAAe,GAAG,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,KAAK,QAAQ;AAEtE,SAAO,EAAE,MAAM,IAAI,MAAM,MAAM;AACjC;AAoDA,SAAS,eAAe,GAAG;AACzB,MAAI,GAAG,GAAG,IACR,kBAAkB,EAAE,SAAS,GAC7B,MAAM,IACN,IAAI,EAAE,CAAC;AAET,MAAI,kBAAkB,GAAG;AACvB,WAAO;AACP,SAAK,IAAI,GAAG,IAAI,iBAAiB,KAAK;AACpC,WAAK,EAAE,CAAC,IAAI;AACZ,UAAI,WAAW,GAAG;AAClB,UAAI;AAAG,eAAO,cAAc,CAAC;AAC7B,aAAO;AAAA,IACT;AAEA,QAAI,EAAE,CAAC;AACP,SAAK,IAAI;AACT,QAAI,WAAW,GAAG;AAClB,QAAI;AAAG,aAAO,cAAc,CAAC;AAAA,EAC/B,WAAW,MAAM,GAAG;AAClB,WAAO;AAAA,EACT;AAGA,SAAO,IAAI,OAAO;AAAI,SAAK;AAE3B,SAAO,MAAM;AACf;AAGA,SAAS,WAAW,GAAGD,MAAKC,MAAK;AAC/B,MAAI,MAAM,CAAC,CAAC,KAAK,IAAID,QAAO,IAAIC,MAAK;AACnC,UAAM,MAAM,kBAAkB,CAAC;AAAA,EACjC;AACF;AAQA,SAAS,oBAAoB,GAAG,GAAG,IAAI,WAAW;AAChD,MAAI,IAAI,GAAG,GAAG;AAGd,OAAK,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK;AAAI,MAAE;AAGnC,MAAI,EAAE,IAAI,GAAG;AACX,SAAK;AACL,SAAK;AAAA,EACP,OAAO;AACL,SAAK,KAAK,MAAM,IAAI,KAAK,QAAQ;AACjC,SAAK;AAAA,EACP;AAKA,MAAI,QAAQ,IAAI,WAAW,CAAC;AAC5B,OAAK,EAAE,EAAE,IAAI,IAAI;AAEjB,MAAI,aAAa,MAAM;AACrB,QAAI,IAAI,GAAG;AACT,UAAI,KAAK;AAAG,aAAK,KAAK,MAAM;AAAA,eACnB,KAAK;AAAG,aAAK,KAAK,KAAK;AAChC,UAAI,KAAK,KAAK,MAAM,SAAS,KAAK,KAAK,MAAM,SAAS,MAAM,OAAS,MAAM;AAAA,IAC7E,OAAO;AACL,WAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,OACnD,EAAE,KAAK,CAAC,IAAI,IAAI,MAAM,MAAM,QAAQ,IAAI,IAAI,CAAC,IAAI,MAC/C,MAAM,IAAI,KAAK,MAAM,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,MAAM,MAAM;AAAA,IAC/D;AAAA,EACF,OAAO;AACL,QAAI,IAAI,GAAG;AACT,UAAI,KAAK;AAAG,aAAK,KAAK,MAAO;AAAA,eACpB,KAAK;AAAG,aAAK,KAAK,MAAM;AAAA,eACxB,KAAK;AAAG,aAAK,KAAK,KAAK;AAChC,WAAK,aAAa,KAAK,MAAM,MAAM,QAAQ,CAAC,aAAa,KAAK,KAAK,MAAM;AAAA,IAC3E,OAAO;AACL,YAAM,aAAa,KAAK,MAAM,KAAK,KAAK,KACvC,CAAC,aAAa,KAAK,KAAM,KAAK,KAAK,IAAI,OACrC,EAAE,KAAK,CAAC,IAAI,IAAI,MAAO,MAAM,QAAQ,IAAI,IAAI,CAAC,IAAI;AAAA,IACvD;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,YAAY,KAAK,QAAQ,SAAS;AACzC,MAAI,GACF,MAAM,CAAC,CAAC,GACR,MACA,IAAI,GACJ,OAAO,IAAI;AAEb,SAAO,IAAI,QAAO;AAChB,SAAK,OAAO,IAAI,QAAQ;AAAS,UAAI,IAAI,KAAK;AAC9C,QAAI,CAAC,KAAK,SAAS,QAAQ,IAAI,OAAO,GAAG,CAAC;AAC1C,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,UAAI,IAAI,CAAC,IAAI,UAAU,GAAG;AACxB,YAAI,IAAI,IAAI,CAAC,MAAM;AAAQ,cAAI,IAAI,CAAC,IAAI;AACxC,YAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU;AACjC,YAAI,CAAC,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,QAAQ;AACrB;AAQA,SAAS,OAAO,MAAM,GAAG;AACvB,MAAI,GAAG,KAAK;AAEZ,MAAI,EAAE,OAAO;AAAG,WAAO;AAMvB,QAAM,EAAE,EAAE;AACV,MAAI,MAAM,IAAI;AACZ,QAAI,KAAK,KAAK,MAAM,CAAC;AACrB,SAAK,IAAI,QAAQ,GAAG,CAAC,GAAG,SAAS;AAAA,EACnC,OAAO;AACL,QAAI;AACJ,QAAI;AAAA,EACN;AAEA,OAAK,aAAa;AAElB,MAAI,aAAa,MAAM,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AAGjD,WAAS,IAAI,GAAG,OAAM;AACpB,QAAI,QAAQ,EAAE,MAAM,CAAC;AACrB,QAAI,MAAM,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC;AAAA,EACrD;AAEA,OAAK,aAAa;AAElB,SAAO;AACT;AAMA,IAAI,SAAU,2BAAY;AAGxB,WAAS,gBAAgB,GAAG,GAAG,MAAM;AACnC,QAAI,MACF,QAAQ,GACR,IAAI,EAAE;AAER,SAAK,IAAI,EAAE,MAAM,GAAG,OAAM;AACxB,aAAO,EAAE,CAAC,IAAI,IAAI;AAClB,QAAE,CAAC,IAAI,OAAO,OAAO;AACrB,cAAQ,OAAO,OAAO;AAAA,IACxB;AAEA,QAAI;AAAO,QAAE,QAAQ,KAAK;AAE1B,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ,GAAG,GAAG,IAAI,IAAI;AAC7B,QAAI,GAAG;AAEP,QAAI,MAAM,IAAI;AACZ,UAAI,KAAK,KAAK,IAAI;AAAA,IACpB,OAAO;AACL,WAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB,cAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI;AACtB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,GAAG,GAAG,IAAI,MAAM;AAChC,QAAI,IAAI;AAGR,WAAO,QAAO;AACZ,QAAE,EAAE,KAAK;AACT,UAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI;AACxB,QAAE,EAAE,IAAI,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE;AAAA,IACjC;AAGA,WAAO,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS;AAAI,QAAE,MAAM;AAAA,EACzC;AAEA,SAAO,SAAU,GAAG,GAAG,IAAI,IAAI,IAAI,MAAM;AACvC,QAAI,KAAK,GAAG,GAAG,GAAG,SAAS,MAAM,MAAM,OAAO,GAAG,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,IAAI,IAAI,KACnF,IAAI,IACJ,OAAO,EAAE,aACTC,QAAO,EAAE,KAAK,EAAE,IAAI,IAAI,IACxB,KAAK,EAAE,GACP,KAAK,EAAE;AAGT,QAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;AAElC,aAAO,IAAI;AAAA;AAAA,QACT,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,KAAK,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM;AAAA;AAAA,UAGpD,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC,KAAKA,QAAO,IAAIA,QAAO;AAAA;AAAA,MAAC;AAAA,IACjD;AAEA,QAAI,MAAM;AACR,gBAAU;AACV,UAAI,EAAE,IAAI,EAAE;AAAA,IACd,OAAO;AACL,aAAO;AACP,gBAAU;AACV,UAAI,UAAU,EAAE,IAAI,OAAO,IAAI,UAAU,EAAE,IAAI,OAAO;AAAA,IACxD;AAEA,SAAK,GAAG;AACR,SAAK,GAAG;AACR,QAAI,IAAI,KAAKA,KAAI;AACjB,SAAK,EAAE,IAAI,CAAC;AAIZ,SAAK,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI;AAAI;AAEvC,QAAI,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK;AAAI;AAE1B,QAAI,MAAM,MAAM;AACd,WAAK,KAAK,KAAK;AACf,WAAK,KAAK;AAAA,IACZ,WAAW,IAAI;AACb,WAAK,MAAM,EAAE,IAAI,EAAE,KAAK;AAAA,IAC1B,OAAO;AACL,WAAK;AAAA,IACP;AAEA,QAAI,KAAK,GAAG;AACV,SAAG,KAAK,CAAC;AACT,aAAO;AAAA,IACT,OAAO;AAGL,WAAK,KAAK,UAAU,IAAI;AACxB,UAAI;AAGJ,UAAI,MAAM,GAAG;AACX,YAAI;AACJ,aAAK,GAAG,CAAC;AACT;AAGA,gBAAQ,IAAI,MAAM,MAAM,MAAM,KAAK;AACjC,cAAI,IAAI,QAAQ,GAAG,CAAC,KAAK;AACzB,aAAG,CAAC,IAAI,IAAI,KAAK;AACjB,cAAI,IAAI,KAAK;AAAA,QACf;AAEA,eAAO,KAAK,IAAI;AAAA,MAGlB,OAAO;AAGL,YAAI,QAAQ,GAAG,CAAC,IAAI,KAAK;AAEzB,YAAI,IAAI,GAAG;AACT,eAAK,gBAAgB,IAAI,GAAG,IAAI;AAChC,eAAK,gBAAgB,IAAI,GAAG,IAAI;AAChC,eAAK,GAAG;AACR,eAAK,GAAG;AAAA,QACV;AAEA,aAAK;AACL,cAAM,GAAG,MAAM,GAAG,EAAE;AACpB,eAAO,IAAI;AAGX,eAAO,OAAO;AAAK,cAAI,MAAM,IAAI;AAEjC,aAAK,GAAG,MAAM;AACd,WAAG,QAAQ,CAAC;AACZ,cAAM,GAAG,CAAC;AAEV,YAAI,GAAG,CAAC,KAAK,OAAO;AAAG,YAAE;AAEzB,WAAG;AACD,cAAI;AAGJ,gBAAM,QAAQ,IAAI,KAAK,IAAI,IAAI;AAG/B,cAAI,MAAM,GAAG;AAGX,mBAAO,IAAI,CAAC;AACZ,gBAAI,MAAM;AAAM,qBAAO,OAAO,QAAQ,IAAI,CAAC,KAAK;AAGhD,gBAAI,OAAO,MAAM;AAUjB,gBAAI,IAAI,GAAG;AACT,kBAAI,KAAK;AAAM,oBAAI,OAAO;AAG1B,qBAAO,gBAAgB,IAAI,GAAG,IAAI;AAClC,sBAAQ,KAAK;AACb,qBAAO,IAAI;AAGX,oBAAM,QAAQ,MAAM,KAAK,OAAO,IAAI;AAGpC,kBAAI,OAAO,GAAG;AACZ;AAGA,yBAAS,MAAM,KAAK,QAAQ,KAAK,IAAI,OAAO,IAAI;AAAA,cAClD;AAAA,YACF,OAAO;AAKL,kBAAI,KAAK;AAAG,sBAAM,IAAI;AACtB,qBAAO,GAAG,MAAM;AAAA,YAClB;AAEA,oBAAQ,KAAK;AACb,gBAAI,QAAQ;AAAM,mBAAK,QAAQ,CAAC;AAGhC,qBAAS,KAAK,MAAM,MAAM,IAAI;AAG9B,gBAAI,OAAO,IAAI;AACb,qBAAO,IAAI;AAGX,oBAAM,QAAQ,IAAI,KAAK,IAAI,IAAI;AAG/B,kBAAI,MAAM,GAAG;AACX;AAGA,yBAAS,KAAK,KAAK,OAAO,KAAK,IAAI,MAAM,IAAI;AAAA,cAC/C;AAAA,YACF;AAEA,mBAAO,IAAI;AAAA,UACb,WAAW,QAAQ,GAAG;AACpB;AACA,kBAAM,CAAC,CAAC;AAAA,UACV;AAGA,aAAG,GAAG,IAAI;AAGV,cAAI,OAAO,IAAI,CAAC,GAAG;AACjB,gBAAI,MAAM,IAAI,GAAG,EAAE,KAAK;AAAA,UAC1B,OAAO;AACL,kBAAM,CAAC,GAAG,EAAE,CAAC;AACb,mBAAO;AAAA,UACT;AAAA,QAEF,UAAU,OAAO,MAAM,IAAI,CAAC,MAAM,WAAW;AAE7C,eAAO,IAAI,CAAC,MAAM;AAAA,MACpB;AAGA,UAAI,CAAC,GAAG,CAAC;AAAG,WAAG,MAAM;AAAA,IACvB;AAGA,QAAI,WAAW,GAAG;AAChB,QAAE,IAAI;AACN,gBAAU;AAAA,IACZ,OAAO;AAGL,WAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK;AAAI;AACzC,QAAE,IAAI,IAAI,IAAI,UAAU;AAExB,eAAS,GAAG,KAAK,KAAK,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IAC9C;AAEA,WAAO;AAAA,EACT;AACF,EAAG;AAOF,SAAS,SAAS,GAAG,IAAI,IAAI,aAAa;AACzC,MAAI,QAAQ,GAAG,GAAG,GAAG,IAAI,SAAS,GAAG,IAAI,KACvC,OAAO,EAAE;AAGX;AAAK,QAAI,MAAM,MAAM;AACnB,WAAK,EAAE;AAGP,UAAI,CAAC;AAAI,eAAO;AAWhB,WAAK,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK;AAAI;AAC9C,UAAI,KAAK;AAGT,UAAI,IAAI,GAAG;AACT,aAAK;AACL,YAAI;AACJ,YAAI,GAAG,MAAM,CAAC;AAGd,aAAK,IAAI,QAAQ,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK;AAAA,MAC9C,OAAO;AACL,cAAM,KAAK,MAAM,IAAI,KAAK,QAAQ;AAClC,YAAI,GAAG;AACP,YAAI,OAAO,GAAG;AACZ,cAAI,aAAa;AAGf,mBAAO,OAAO;AAAM,iBAAG,KAAK,CAAC;AAC7B,gBAAI,KAAK;AACT,qBAAS;AACT,iBAAK;AACL,gBAAI,IAAI,WAAW;AAAA,UACrB,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF,OAAO;AACL,cAAI,IAAI,GAAG,GAAG;AAGd,eAAK,SAAS,GAAG,KAAK,IAAI,KAAK;AAAI;AAGnC,eAAK;AAIL,cAAI,IAAI,WAAW;AAGnB,eAAK,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI,SAAS,IAAI,CAAC,IAAI,KAAK;AAAA,QAC1D;AAAA,MACF;AAGA,oBAAc,eAAe,KAAK,KAChC,GAAG,MAAM,CAAC,MAAM,WAAW,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI,SAAS,IAAI,CAAC;AAMvE,gBAAU,KAAK,KACV,MAAM,iBAAiB,MAAM,KAAK,OAAO,EAAE,IAAI,IAAI,IAAI,MACxD,KAAK,KAAK,MAAM,MAAM,MAAM,KAAK,eAAe,MAAM;AAAA,OAGpD,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI,SAAS,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,KAAM,KACvE,OAAO,EAAE,IAAI,IAAI,IAAI;AAE3B,UAAI,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,WAAG,SAAS;AACZ,YAAI,SAAS;AAGX,gBAAM,EAAE,IAAI;AAGZ,aAAG,CAAC,IAAI,QAAQ,KAAK,WAAW,KAAK,YAAY,QAAQ;AACzD,YAAE,IAAI,CAAC,MAAM;AAAA,QACf,OAAO;AAGL,aAAG,CAAC,IAAI,EAAE,IAAI;AAAA,QAChB;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,GAAG;AACV,WAAG,SAAS;AACZ,YAAI;AACJ;AAAA,MACF,OAAO;AACL,WAAG,SAAS,MAAM;AAClB,YAAI,QAAQ,IAAI,WAAW,CAAC;AAI5B,WAAG,GAAG,IAAI,IAAI,KAAK,IAAI,QAAQ,IAAI,SAAS,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI;AAAA,MAC7E;AAEA,UAAI,SAAS;AACX,mBAAS;AAGP,cAAI,OAAO,GAAG;AAGZ,iBAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK;AAAI;AACzC,gBAAI,GAAG,CAAC,KAAK;AACb,iBAAK,IAAI,GAAG,KAAK,IAAI,KAAK;AAAI;AAG9B,gBAAI,KAAK,GAAG;AACV,gBAAE;AACF,kBAAI,GAAG,CAAC,KAAK;AAAM,mBAAG,CAAC,IAAI;AAAA,YAC7B;AAEA;AAAA,UACF,OAAO;AACL,eAAG,GAAG,KAAK;AACX,gBAAI,GAAG,GAAG,KAAK;AAAM;AACrB,eAAG,KAAK,IAAI;AACZ,gBAAI;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAGA,WAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,MAAM;AAAI,WAAG,IAAI;AAAA,IAC7C;AAEA,MAAI,UAAU;AAGZ,QAAI,EAAE,IAAI,KAAK,MAAM;AAGnB,QAAE,IAAI;AACN,QAAE,IAAI;AAAA,IAGR,WAAW,EAAE,IAAI,KAAK,MAAM;AAG1B,QAAE,IAAI;AACN,QAAE,IAAI,CAAC,CAAC;AAAA,IAEV;AAAA,EACF;AAEA,SAAO;AACT;AAGA,SAAS,eAAe,GAAG,OAAO,IAAI;AACpC,MAAI,CAAC,EAAE,SAAS;AAAG,WAAO,kBAAkB,CAAC;AAC7C,MAAI,GACF,IAAI,EAAE,GACN,MAAM,eAAe,EAAE,CAAC,GACxB,MAAM,IAAI;AAEZ,MAAI,OAAO;AACT,QAAI,OAAO,IAAI,KAAK,OAAO,GAAG;AAC5B,YAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,cAAc,CAAC;AAAA,IAC5D,WAAW,MAAM,GAAG;AAClB,YAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,IACzC;AAEA,UAAM,OAAO,EAAE,IAAI,IAAI,MAAM,QAAQ,EAAE;AAAA,EACzC,WAAW,IAAI,GAAG;AAChB,UAAM,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI;AACrC,QAAI,OAAO,IAAI,KAAK,OAAO;AAAG,aAAO,cAAc,CAAC;AAAA,EACtD,WAAW,KAAK,KAAK;AACnB,WAAO,cAAc,IAAI,IAAI,GAAG;AAChC,QAAI,OAAO,IAAI,KAAK,IAAI,KAAK;AAAG,YAAM,MAAM,MAAM,cAAc,CAAC;AAAA,EACnE,OAAO;AACL,SAAK,IAAI,IAAI,KAAK;AAAK,YAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAChE,QAAI,OAAO,IAAI,KAAK,OAAO,GAAG;AAC5B,UAAI,IAAI,MAAM;AAAK,eAAO;AAC1B,aAAO,cAAc,CAAC;AAAA,IACxB;AAAA,EACF;AAEA,SAAO;AACT;AAIA,SAAS,kBAAkB,QAAQ,GAAG;AACpC,MAAI,IAAI,OAAO,CAAC;AAGhB,OAAM,KAAK,UAAU,KAAK,IAAI,KAAK;AAAI;AACvC,SAAO;AACT;AAGA,SAAS,QAAQ,MAAM,IAAI,IAAI;AAC7B,MAAI,KAAK,gBAAgB;AAGvB,eAAW;AACX,QAAI;AAAI,WAAK,YAAY;AACzB,UAAM,MAAM,sBAAsB;AAAA,EACpC;AACA,SAAO,SAAS,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAC7C;AAGA,SAAS,MAAM,MAAM,IAAI,IAAI;AAC3B,MAAI,KAAK;AAAc,UAAM,MAAM,sBAAsB;AACzD,SAAO,SAAS,IAAI,KAAK,EAAE,GAAG,IAAI,IAAI,IAAI;AAC5C;AAGA,SAAS,aAAa,QAAQ;AAC5B,MAAI,IAAI,OAAO,SAAS,GACtB,MAAM,IAAI,WAAW;AAEvB,MAAI,OAAO,CAAC;AAGZ,MAAI,GAAG;AAGL,WAAO,IAAI,MAAM,GAAG,KAAK;AAAI;AAG7B,SAAK,IAAI,OAAO,CAAC,GAAG,KAAK,IAAI,KAAK;AAAI;AAAA,EACxC;AAEA,SAAO;AACT;AAGA,SAAS,cAAc,GAAG;AACxB,MAAI,KAAK;AACT,SAAO;AAAM,UAAM;AACnB,SAAO;AACT;AAUA,SAAS,OAAO,MAAM,GAAG,GAAG,IAAI;AAC9B,MAAI,aACF,IAAI,IAAI,KAAK,CAAC,GAId,IAAI,KAAK,KAAK,KAAK,WAAW,CAAC;AAEjC,aAAW;AAEX,aAAS;AACP,QAAI,IAAI,GAAG;AACT,UAAI,EAAE,MAAM,CAAC;AACb,UAAI,SAAS,EAAE,GAAG,CAAC;AAAG,sBAAc;AAAA,IACtC;AAEA,QAAI,UAAU,IAAI,CAAC;AACnB,QAAI,MAAM,GAAG;AAGX,UAAI,EAAE,EAAE,SAAS;AACjB,UAAI,eAAe,EAAE,EAAE,CAAC,MAAM;AAAG,UAAE,EAAE,EAAE,CAAC;AACxC;AAAA,IACF;AAEA,QAAI,EAAE,MAAM,CAAC;AACb,aAAS,EAAE,GAAG,CAAC;AAAA,EACjB;AAEA,aAAW;AAEX,SAAO;AACT;AAGA,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,IAAI;AAC/B;AAMA,SAAS,SAAS,MAAM,MAAM,GAAG;AAC/B,MAAI,GAAG,GACL,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,GACpB,IAAI;AAEN,SAAO,EAAE,IAAI,KAAK,UAAS;AACzB,QAAI,IAAI,KAAK,KAAK,CAAC,CAAC;AAGpB,QAAI,CAAC,EAAE,GAAG;AACR,UAAI;AACJ;AAAA,IACF;AAEA,QAAI,EAAE,IAAI,CAAC;AAEX,QAAI,MAAM,KAAK,MAAM,KAAK,EAAE,MAAM,GAAG;AACnC,UAAI;AAAA,IACN;AAAA,EACF;AAEA,SAAO;AACT;AAkCA,SAAS,mBAAmB,GAAG,IAAI;AACjC,MAAI,aAAa,OAAO,GAAGC,MAAKC,MAAK,GAAG,KACtC,MAAM,GACN,IAAI,GACJ,IAAI,GACJ,OAAO,EAAE,aACT,KAAK,KAAK,UACV,KAAK,KAAK;AAGZ,MAAI,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,IAAI;AAE/B,WAAO,IAAI,KAAK,EAAE,IACd,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAChC,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AAAA,EACnC;AAEA,MAAI,MAAM,MAAM;AACd,eAAW;AACX,UAAM;AAAA,EACR,OAAO;AACL,UAAM;AAAA,EACR;AAEA,MAAI,IAAI,KAAK,OAAO;AAGpB,SAAO,EAAE,IAAI,IAAI;AAGf,QAAI,EAAE,MAAM,CAAC;AACb,SAAK;AAAA,EACP;AAIA,UAAQ,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI;AACtD,SAAO;AACP,gBAAcD,OAAMC,OAAM,IAAI,KAAK,CAAC;AACpC,OAAK,YAAY;AAEjB,aAAS;AACP,IAAAD,OAAM,SAASA,KAAI,MAAM,CAAC,GAAG,KAAK,CAAC;AACnC,kBAAc,YAAY,MAAM,EAAE,CAAC;AACnC,QAAIC,KAAI,KAAK,OAAOD,MAAK,aAAa,KAAK,CAAC,CAAC;AAE7C,QAAI,eAAe,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,MAAM,eAAeC,KAAI,CAAC,EAAE,MAAM,GAAG,GAAG,GAAG;AAC7E,UAAI;AACJ,aAAO;AAAK,QAAAA,OAAM,SAASA,KAAI,MAAMA,IAAG,GAAG,KAAK,CAAC;AAOjD,UAAI,MAAM,MAAM;AAEd,YAAI,MAAM,KAAK,oBAAoBA,KAAI,GAAG,MAAM,OAAO,IAAI,GAAG,GAAG;AAC/D,eAAK,YAAY,OAAO;AACxB,wBAAcD,OAAM,IAAI,IAAI,KAAK,CAAC;AAClC,cAAI;AACJ;AAAA,QACF,OAAO;AACL,iBAAO,SAASC,MAAK,KAAK,YAAY,IAAI,IAAI,WAAW,IAAI;AAAA,QAC/D;AAAA,MACF,OAAO;AACL,aAAK,YAAY;AACjB,eAAOA;AAAA,MACT;AAAA,IACF;AAEA,IAAAA,OAAM;AAAA,EACR;AACF;AAkBA,SAAS,iBAAiB,GAAG,IAAI;AAC/B,MAAI,GAAG,IAAI,aAAa,GAAG,WAAW,KAAKA,MAAK,GAAG,KAAK,IAAI,IAC1D,IAAI,GACJ,QAAQ,IACR,IAAI,GACJ,KAAK,EAAE,GACP,OAAO,EAAE,aACT,KAAK,KAAK,UACV,KAAK,KAAK;AAGZ,MAAI,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,UAAU,GAAG;AACpE,WAAO,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EACrE;AAEA,MAAI,MAAM,MAAM;AACd,eAAW;AACX,UAAM;AAAA,EACR,OAAO;AACL,UAAM;AAAA,EACR;AAEA,OAAK,YAAY,OAAO;AACxB,MAAI,eAAe,EAAE;AACrB,OAAK,EAAE,OAAO,CAAC;AAEf,MAAI,KAAK,IAAI,IAAI,EAAE,CAAC,IAAI,OAAQ;AAa9B,WAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG;AACtD,UAAI,EAAE,MAAM,CAAC;AACb,UAAI,eAAe,EAAE,CAAC;AACtB,WAAK,EAAE,OAAO,CAAC;AACf;AAAA,IACF;AAEA,QAAI,EAAE;AAEN,QAAI,KAAK,GAAG;AACV,UAAI,IAAI,KAAK,OAAO,CAAC;AACrB;AAAA,IACF,OAAO;AACL,UAAI,IAAI,KAAK,KAAK,MAAM,EAAE,MAAM,CAAC,CAAC;AAAA,IACpC;AAAA,EACF,OAAO;AAKL,QAAI,QAAQ,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,IAAI,EAAE;AAC3C,QAAI,iBAAiB,IAAI,KAAK,KAAK,MAAM,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE,KAAK,CAAC;AACzE,SAAK,YAAY;AAEjB,WAAO,MAAM,OAAO,SAAS,GAAG,IAAI,IAAI,WAAW,IAAI,IAAI;AAAA,EAC7D;AAGA,OAAK;AAKL,EAAAA,OAAM,YAAY,IAAI,OAAO,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;AAC1D,OAAK,SAAS,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;AAChC,gBAAc;AAEd,aAAS;AACP,gBAAY,SAAS,UAAU,MAAM,EAAE,GAAG,KAAK,CAAC;AAChD,QAAIA,KAAI,KAAK,OAAO,WAAW,IAAI,KAAK,WAAW,GAAG,KAAK,CAAC,CAAC;AAE7D,QAAI,eAAe,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,MAAM,eAAeA,KAAI,CAAC,EAAE,MAAM,GAAG,GAAG,GAAG;AAC7E,MAAAA,OAAMA,KAAI,MAAM,CAAC;AAIjB,UAAI,MAAM;AAAG,QAAAA,OAAMA,KAAI,KAAK,QAAQ,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC;AACpE,MAAAA,OAAM,OAAOA,MAAK,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC;AAQrC,UAAI,MAAM,MAAM;AACd,YAAI,oBAAoBA,KAAI,GAAG,MAAM,OAAO,IAAI,GAAG,GAAG;AACpD,eAAK,YAAY,OAAO;AACxB,cAAI,YAAY,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC;AAC1D,eAAK,SAAS,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;AAChC,wBAAc,MAAM;AAAA,QACtB,OAAO;AACL,iBAAO,SAASA,MAAK,KAAK,YAAY,IAAI,IAAI,WAAW,IAAI;AAAA,QAC/D;AAAA,MACF,OAAO;AACL,aAAK,YAAY;AACjB,eAAOA;AAAA,MACT;AAAA,IACF;AAEA,IAAAA,OAAM;AACN,mBAAe;AAAA,EACjB;AACF;AAIA,SAAS,kBAAkB,GAAG;AAE5B,SAAO,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;AAC7B;AAMA,SAAS,aAAa,GAAG,KAAK;AAC5B,MAAI,GAAG,GAAG;AAIV,OAAK,IAAI,IAAI,QAAQ,GAAG,KAAK;AAAI,UAAM,IAAI,QAAQ,KAAK,EAAE;AAG1D,OAAK,IAAI,IAAI,OAAO,IAAI,KAAK,GAAG;AAG9B,QAAI,IAAI;AAAG,UAAI;AACf,SAAK,CAAC,IAAI,MAAM,IAAI,CAAC;AACrB,UAAM,IAAI,UAAU,GAAG,CAAC;AAAA,EAC1B,WAAW,IAAI,GAAG;AAGhB,QAAI,IAAI;AAAA,EACV;AAGA,OAAK,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,IAAI;AAAI;AAG1C,OAAK,MAAM,IAAI,QAAQ,IAAI,WAAW,MAAM,CAAC,MAAM,IAAI,EAAE;AAAI;AAC7D,QAAM,IAAI,MAAM,GAAG,GAAG;AAEtB,MAAI,KAAK;AACP,WAAO;AACP,MAAE,IAAI,IAAI,IAAI,IAAI;AAClB,MAAE,IAAI,CAAC;AAMP,SAAK,IAAI,KAAK;AACd,QAAI,IAAI;AAAG,WAAK;AAEhB,QAAI,IAAI,KAAK;AACX,UAAI;AAAG,UAAE,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAChC,WAAK,OAAO,UAAU,IAAI;AAAM,UAAE,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,KAAK,QAAQ,CAAC;AACrE,YAAM,IAAI,MAAM,CAAC;AACjB,UAAI,WAAW,IAAI;AAAA,IACrB,OAAO;AACL,WAAK;AAAA,IACP;AAEA,WAAO;AAAM,aAAO;AACpB,MAAE,EAAE,KAAK,CAAC,GAAG;AAEb,QAAI,UAAU;AAGZ,UAAI,EAAE,IAAI,EAAE,YAAY,MAAM;AAG5B,UAAE,IAAI;AACN,UAAE,IAAI;AAAA,MAGR,WAAW,EAAE,IAAI,EAAE,YAAY,MAAM;AAGnC,UAAE,IAAI;AACN,UAAE,IAAI,CAAC,CAAC;AAAA,MAEV;AAAA,IACF;AAAA,EACF,OAAO;AAGL,MAAE,IAAI;AACN,MAAE,IAAI,CAAC,CAAC;AAAA,EACV;AAEA,SAAO;AACT;AAMA,SAAS,WAAW,GAAG,KAAK;AAC1B,MAAI,MAAM,MAAM,SAAS,GAAG,SAAS,KAAK,GAAG,IAAI;AAEjD,MAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,UAAM,IAAI,QAAQ,gBAAgB,IAAI;AACtC,QAAI,UAAU,KAAK,GAAG;AAAG,aAAO,aAAa,GAAG,GAAG;AAAA,EACrD,WAAW,QAAQ,cAAc,QAAQ,OAAO;AAC9C,QAAI,CAAC,CAAC;AAAK,QAAE,IAAI;AACjB,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK,GAAG,GAAI;AACpB,WAAO;AACP,UAAM,IAAI,YAAY;AAAA,EACxB,WAAW,SAAS,KAAK,GAAG,GAAI;AAC9B,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK,GAAG,GAAI;AAC7B,WAAO;AAAA,EACT,OAAO;AACL,UAAM,MAAM,kBAAkB,GAAG;AAAA,EACnC;AAGA,MAAI,IAAI,OAAO,IAAI;AAEnB,MAAI,IAAI,GAAG;AACT,QAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AACpB,UAAM,IAAI,UAAU,GAAG,CAAC;AAAA,EAC1B,OAAO;AACL,UAAM,IAAI,MAAM,CAAC;AAAA,EACnB;AAIA,MAAI,IAAI,QAAQ,GAAG;AACnB,YAAU,KAAK;AACf,SAAO,EAAE;AAET,MAAI,SAAS;AACX,UAAM,IAAI,QAAQ,KAAK,EAAE;AACzB,UAAM,IAAI;AACV,QAAI,MAAM;AAGV,cAAU,OAAO,MAAM,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC;AAAA,EACjD;AAEA,OAAK,YAAY,KAAK,MAAM,IAAI;AAChC,OAAK,GAAG,SAAS;AAGjB,OAAK,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE;AAAG,OAAG,IAAI;AACtC,MAAI,IAAI;AAAG,WAAO,IAAI,KAAK,EAAE,IAAI,CAAC;AAClC,IAAE,IAAI,kBAAkB,IAAI,EAAE;AAC9B,IAAE,IAAI;AACN,aAAW;AAQX,MAAI;AAAS,QAAI,OAAO,GAAG,SAAS,MAAM,CAAC;AAG3C,MAAI;AAAG,QAAI,EAAE,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,QAAQ,GAAG,CAAC,IAAI,QAAQ,IAAI,GAAG,CAAC,CAAC;AACvE,aAAW;AAEX,SAAO;AACT;AAQA,SAAS,KAAK,MAAM,GAAG;AACrB,MAAI,GACF,MAAM,EAAE,EAAE;AAEZ,MAAI,MAAM,GAAG;AACX,WAAO,EAAE,OAAO,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,CAAC;AAAA,EACpD;AAOA,MAAI,MAAM,KAAK,KAAK,GAAG;AACvB,MAAI,IAAI,KAAK,KAAK,IAAI;AAEtB,MAAI,EAAE,MAAM,IAAI,QAAQ,GAAG,CAAC,CAAC;AAC7B,MAAI,aAAa,MAAM,GAAG,GAAG,CAAC;AAG9B,MAAI,QACF,KAAK,IAAI,KAAK,CAAC,GACf,MAAM,IAAI,KAAK,EAAE,GACjB,MAAM,IAAI,KAAK,EAAE;AACnB,SAAO,OAAM;AACX,aAAS,EAAE,MAAM,CAAC;AAClB,QAAI,EAAE,MAAM,GAAG,KAAK,OAAO,MAAM,IAAI,MAAM,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,EACjE;AAEA,SAAO;AACT;AAIA,SAAS,aAAa,MAAM,GAAG,GAAG,GAAG,cAAc;AACjD,MAAI,GAAG,GAAG,GAAG,IACX,IAAI,GACJ,KAAK,KAAK,WACV,IAAI,KAAK,KAAK,KAAK,QAAQ;AAE7B,aAAW;AACX,OAAK,EAAE,MAAM,CAAC;AACd,MAAI,IAAI,KAAK,CAAC;AAEd,aAAS;AACP,QAAI,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;AAClD,QAAI,eAAe,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC;AACxC,QAAI,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;AAClD,QAAI,EAAE,KAAK,CAAC;AAEZ,QAAI,EAAE,EAAE,CAAC,MAAM,QAAQ;AACrB,WAAK,IAAI,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK;AAAK;AACtC,UAAI,KAAK;AAAI;AAAA,IACf;AAEA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ;AAAA,EACF;AAEA,aAAW;AACX,IAAE,EAAE,SAAS,IAAI;AAEjB,SAAO;AACT;AAIA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI;AACR,SAAO,EAAE;AAAG,SAAK;AACjB,SAAO;AACT;AAIA,SAAS,iBAAiB,MAAM,GAAG;AACjC,MAAI,GACF,QAAQ,EAAE,IAAI,GACd,KAAK,MAAM,MAAM,KAAK,WAAW,CAAC,GAClC,SAAS,GAAG,MAAM,GAAG;AAEvB,MAAI,EAAE,IAAI;AAEV,MAAI,EAAE,IAAI,MAAM,GAAG;AACjB,eAAW,QAAQ,IAAI;AACvB,WAAO;AAAA,EACT;AAEA,MAAI,EAAE,SAAS,EAAE;AAEjB,MAAI,EAAE,OAAO,GAAG;AACd,eAAW,QAAQ,IAAI;AAAA,EACzB,OAAO;AACL,QAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAGvB,QAAI,EAAE,IAAI,MAAM,GAAG;AACjB,iBAAW,MAAM,CAAC,IAAK,QAAQ,IAAI,IAAM,QAAQ,IAAI;AACrD,aAAO;AAAA,IACT;AAEA,eAAW,MAAM,CAAC,IAAK,QAAQ,IAAI,IAAM,QAAQ,IAAI;AAAA,EACvD;AAEA,SAAO,EAAE,MAAM,EAAE,EAAE,IAAI;AACzB;AAQA,SAAS,eAAe,GAAG,SAAS,IAAI,IAAI;AAC1C,MAAI,MAAM,GAAG,GAAG,GAAG,KAAK,SAAS,KAAK,IAAI,GACxC,OAAO,EAAE,aACT,QAAQ,OAAO;AAEjB,MAAI,OAAO;AACT,eAAW,IAAI,GAAG,UAAU;AAC5B,QAAI,OAAO;AAAQ,WAAK,KAAK;AAAA;AACxB,iBAAW,IAAI,GAAG,CAAC;AAAA,EAC1B,OAAO;AACL,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACZ;AAEA,MAAI,CAAC,EAAE,SAAS,GAAG;AACjB,UAAM,kBAAkB,CAAC;AAAA,EAC3B,OAAO;AACL,UAAM,eAAe,CAAC;AACtB,QAAI,IAAI,QAAQ,GAAG;AAOnB,QAAI,OAAO;AACT,aAAO;AACP,UAAI,WAAW,IAAI;AACjB,aAAK,KAAK,IAAI;AAAA,MAChB,WAAW,WAAW,GAAG;AACvB,aAAK,KAAK,IAAI;AAAA,MAChB;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAMA,QAAI,KAAK,GAAG;AACV,YAAM,IAAI,QAAQ,KAAK,EAAE;AACzB,UAAI,IAAI,KAAK,CAAC;AACd,QAAE,IAAI,IAAI,SAAS;AACnB,QAAE,IAAI,YAAY,eAAe,CAAC,GAAG,IAAI,IAAI;AAC7C,QAAE,IAAI,EAAE,EAAE;AAAA,IACZ;AAEA,SAAK,YAAY,KAAK,IAAI,IAAI;AAC9B,QAAI,MAAM,GAAG;AAGb,WAAO,GAAG,EAAE,GAAG,KAAK;AAAI,SAAG,IAAI;AAE/B,QAAI,CAAC,GAAG,CAAC,GAAG;AACV,YAAM,QAAQ,SAAS;AAAA,IACzB,OAAO;AACL,UAAI,IAAI,GAAG;AACT;AAAA,MACF,OAAO;AACL,YAAI,IAAI,KAAK,CAAC;AACd,UAAE,IAAI;AACN,UAAE,IAAI;AACN,YAAI,OAAO,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI;AAChC,aAAK,EAAE;AACP,YAAI,EAAE;AACN,kBAAU;AAAA,MACZ;AAGA,UAAI,GAAG,EAAE;AACT,UAAI,OAAO;AACX,gBAAU,WAAW,GAAG,KAAK,CAAC,MAAM;AAEpC,gBAAU,KAAK,KACV,MAAM,UAAU,aAAa,OAAO,KAAK,QAAQ,EAAE,IAAI,IAAI,IAAI,MAChE,IAAI,KAAK,MAAM,MAAM,OAAO,KAAK,WAAW,OAAO,KAAK,GAAG,KAAK,CAAC,IAAI,KACrE,QAAQ,EAAE,IAAI,IAAI,IAAI;AAE1B,SAAG,SAAS;AAEZ,UAAI,SAAS;AAGX,eAAO,EAAE,GAAG,EAAE,EAAE,IAAI,OAAO,KAAI;AAC7B,aAAG,EAAE,IAAI;AACT,cAAI,CAAC,IAAI;AACP,cAAE;AACF,eAAG,QAAQ,CAAC;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAGA,WAAK,MAAM,GAAG,QAAQ,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE;AAAI;AAG1C,WAAK,IAAI,GAAG,MAAM,IAAI,IAAI,KAAK;AAAK,eAAO,SAAS,OAAO,GAAG,CAAC,CAAC;AAGhE,UAAI,OAAO;AACT,YAAI,MAAM,GAAG;AACX,cAAI,WAAW,MAAM,WAAW,GAAG;AACjC,gBAAI,WAAW,KAAK,IAAI;AACxB,iBAAK,EAAE,KAAK,MAAM,GAAG;AAAO,qBAAO;AACnC,iBAAK,YAAY,KAAK,MAAM,OAAO;AACnC,iBAAK,MAAM,GAAG,QAAQ,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE;AAAI;AAG1C,iBAAK,IAAI,GAAG,MAAM,MAAM,IAAI,KAAK;AAAK,qBAAO,SAAS,OAAO,GAAG,CAAC,CAAC;AAAA,UACpE,OAAO;AACL,kBAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,UACzC;AAAA,QACF;AAEA,cAAO,OAAO,IAAI,IAAI,MAAM,QAAQ;AAAA,MACtC,WAAW,IAAI,GAAG;AAChB,eAAO,EAAE;AAAI,gBAAM,MAAM;AACzB,cAAM,OAAO;AAAA,MACf,OAAO;AACL,YAAI,EAAE,IAAI;AAAK,eAAK,KAAK,KAAK;AAAO,mBAAO;AAAA,iBACnC,IAAI;AAAK,gBAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,MAC7D;AAAA,IACF;AAEA,WAAO,WAAW,KAAK,OAAO,WAAW,IAAI,OAAO,WAAW,IAAI,OAAO,MAAM;AAAA,EAClF;AAEA,SAAO,EAAE,IAAI,IAAI,MAAM,MAAM;AAC/B;AAIA,SAAS,SAAS,KAAK,KAAK;AAC1B,MAAI,IAAI,SAAS,KAAK;AACpB,QAAI,SAAS;AACb,WAAO;AAAA,EACT;AACF;AAyDA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI;AACzB;AASA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AAUA,SAAS,MAAM,GAAG;AAChB,SAAO,IAAI,KAAK,CAAC,EAAE,MAAM;AAC3B;AAWA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK,CAAC;AAC3B;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AAUA,SAAS,MAAM,GAAG;AAChB,SAAO,IAAI,KAAK,CAAC,EAAE,MAAM;AAC3B;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AAUA,SAAS,MAAM,GAAG;AAChB,SAAO,IAAI,KAAK,CAAC,EAAE,MAAM;AAC3B;AA4BA,SAAS,MAAM,GAAG,GAAG;AACnB,MAAI,IAAI,KAAK,CAAC;AACd,MAAI,IAAI,KAAK,CAAC;AACd,MAAI,GACF,KAAK,KAAK,WACV,KAAK,KAAK,UACV,MAAM,KAAK;AAGb,MAAI,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG;AAChB,QAAI,IAAI,KAAK,GAAG;AAAA,EAGlB,WAAW,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG;AACvB,QAAI,MAAM,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,IAAI,IAAI,OAAO,IAAI;AACnD,MAAE,IAAI,EAAE;AAAA,EAGV,WAAW,CAAC,EAAE,KAAK,EAAE,OAAO,GAAG;AAC7B,QAAI,EAAE,IAAI,IAAI,MAAM,MAAM,IAAI,EAAE,IAAI,IAAI,KAAK,CAAC;AAC9C,MAAE,IAAI,EAAE;AAAA,EAGV,WAAW,CAAC,EAAE,KAAK,EAAE,OAAO,GAAG;AAC7B,QAAI,MAAM,MAAM,KAAK,CAAC,EAAE,MAAM,GAAG;AACjC,MAAE,IAAI,EAAE;AAAA,EAGV,WAAW,EAAE,IAAI,GAAG;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,KAAK,OAAO,GAAG,GAAG,KAAK,CAAC,CAAC;AAClC,QAAI,MAAM,MAAM,KAAK,CAAC;AACtB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,QAAI,EAAE,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC;AAAA,EACrC,OAAO;AACL,QAAI,KAAK,KAAK,OAAO,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,EACpC;AAEA,SAAO;AACT;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AASA,SAAS,KAAK,GAAG;AACf,SAAO,SAAS,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC7C;AAWA,SAAS,MAAM,GAAGJ,MAAKC,MAAK;AAC1B,SAAO,IAAI,KAAK,CAAC,EAAE,MAAMD,MAAKC,IAAG;AACnC;AAqBA,SAAS,OAAO,KAAK;AACnB,MAAI,CAAC,OAAO,OAAO,QAAQ;AAAU,UAAM,MAAM,eAAe,iBAAiB;AACjF,MAAI,GAAG,GAAG,GACR,cAAc,IAAI,aAAa,MAC/B,KAAK;AAAA,IACH;AAAA,IAAa;AAAA,IAAG;AAAA,IAChB;AAAA,IAAY;AAAA,IAAG;AAAA,IACf;AAAA,IAAY,CAAC;AAAA,IAAW;AAAA,IACxB;AAAA,IAAY;AAAA,IAAG;AAAA,IACf;AAAA,IAAQ;AAAA,IAAG;AAAA,IACX;AAAA,IAAQ,CAAC;AAAA,IAAW;AAAA,IACpB;AAAA,IAAU;AAAA,IAAG;AAAA,EACf;AAEF,OAAK,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK,GAAG;AACjC,QAAI,IAAI,GAAG,CAAC,GAAG;AAAa,WAAK,CAAC,IAAI,SAAS,CAAC;AAChD,SAAK,IAAI,IAAI,CAAC,OAAO,QAAQ;AAC3B,UAAI,UAAU,CAAC,MAAM,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC;AAAG,aAAK,CAAC,IAAI;AAAA;AACjE,cAAM,MAAM,kBAAkB,IAAI,OAAO,CAAC;AAAA,IACjD;AAAA,EACF;AAEA,MAAI,IAAI,UAAU;AAAa,SAAK,CAAC,IAAI,SAAS,CAAC;AACnD,OAAK,IAAI,IAAI,CAAC,OAAO,QAAQ;AAC3B,QAAI,MAAM,QAAQ,MAAM,SAAS,MAAM,KAAK,MAAM,GAAG;AACnD,UAAI,GAAG;AACL,YAAI,OAAO,UAAU,eAAe,WACjC,OAAO,mBAAmB,OAAO,cAAc;AAChD,eAAK,CAAC,IAAI;AAAA,QACZ,OAAO;AACL,gBAAM,MAAM,iBAAiB;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,aAAK,CAAC,IAAI;AAAA,MACZ;AAAA,IACF,OAAO;AACL,YAAM,MAAM,kBAAkB,IAAI,OAAO,CAAC;AAAA,IAC5C;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI;AACzB;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AAQA,SAAS,MAAM,KAAK;AAClB,MAAI,GAAG,GAAG;AASV,WAASI,SAAQ,GAAG;AAClB,QAAI,GAAGC,IAAG,GACR,IAAI;AAGN,QAAI,EAAE,aAAaD;AAAU,aAAO,IAAIA,SAAQ,CAAC;AAIjD,MAAE,cAAcA;AAEhB,QAAI,kBAAkB,CAAC,GAAG;AACxB,QAAE,IAAI,EAAE;AAER,UAAI,UAAU;AACZ,YAAI,CAAC,EAAE,KAAK,EAAE,IAAIA,SAAQ,MAAM;AAG9B,YAAE,IAAI;AACN,YAAE,IAAI;AAAA,QACR,WAAW,EAAE,IAAIA,SAAQ,MAAM;AAG7B,YAAE,IAAI;AACN,YAAE,IAAI,CAAC,CAAC;AAAA,QACV,OAAO;AACL,YAAE,IAAI,EAAE;AACR,YAAE,IAAI,EAAE,EAAE,MAAM;AAAA,QAClB;AAAA,MACF,OAAO;AACL,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE,IAAI,EAAE,EAAE,MAAM,IAAI,EAAE;AAAA,MAC9B;AAEA;AAAA,IACF;AAEA,QAAI,OAAO;AAEX,QAAI,MAAM,UAAU;AAClB,UAAI,MAAM,GAAG;AACX,UAAE,IAAI,IAAI,IAAI,IAAI,KAAK;AACvB,UAAE,IAAI;AACN,UAAE,IAAI,CAAC,CAAC;AACR;AAAA,MACF;AAEA,UAAI,IAAI,GAAG;AACT,YAAI,CAAC;AACL,UAAE,IAAI;AAAA,MACR,OAAO;AACL,UAAE,IAAI;AAAA,MACR;AAGA,UAAI,MAAM,CAAC,CAAC,KAAK,IAAI,KAAK;AACxB,aAAK,IAAI,GAAGC,KAAI,GAAGA,MAAK,IAAIA,MAAK;AAAI;AAErC,YAAI,UAAU;AACZ,cAAI,IAAID,SAAQ,MAAM;AACpB,cAAE,IAAI;AACN,cAAE,IAAI;AAAA,UACR,WAAW,IAAIA,SAAQ,MAAM;AAC3B,cAAE,IAAI;AACN,cAAE,IAAI,CAAC,CAAC;AAAA,UACV,OAAO;AACL,cAAE,IAAI;AACN,cAAE,IAAI,CAAC,CAAC;AAAA,UACV;AAAA,QACF,OAAO;AACL,YAAE,IAAI;AACN,YAAE,IAAI,CAAC,CAAC;AAAA,QACV;AAEA;AAAA,MACF;AAGA,UAAI,IAAI,MAAM,GAAG;AACf,YAAI,CAAC;AAAG,YAAE,IAAI;AACd,UAAE,IAAI;AACN,UAAE,IAAI;AACN;AAAA,MACF;AAEA,aAAO,aAAa,GAAG,EAAE,SAAS,CAAC;AAAA,IACrC;AAEA,QAAI,MAAM,UAAU;AAClB,WAAKC,KAAI,EAAE,WAAW,CAAC,OAAO,IAAI;AAChC,YAAI,EAAE,MAAM,CAAC;AACb,UAAE,IAAI;AAAA,MACR,OAAO;AACL,YAAIA,OAAM;AAAI,cAAI,EAAE,MAAM,CAAC;AAC3B,UAAE,IAAI;AAAA,MACR;AAEA,aAAO,UAAU,KAAK,CAAC,IAAI,aAAa,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC;AAAA,IACjE;AAEA,QAAI,MAAM,UAAU;AAClB,UAAI,IAAI,GAAG;AACT,YAAI,CAAC;AACL,UAAE,IAAI;AAAA,MACR,OAAO;AACL,UAAE,IAAI;AAAA,MACR;AAEA,aAAO,aAAa,GAAG,EAAE,SAAS,CAAC;AAAA,IACrC;AAEA,UAAM,MAAM,kBAAkB,CAAC;AAAA,EACjC;AAEA,EAAAD,SAAQ,YAAY;AAEpB,EAAAA,SAAQ,WAAW;AACnB,EAAAA,SAAQ,aAAa;AACrB,EAAAA,SAAQ,aAAa;AACrB,EAAAA,SAAQ,cAAc;AACtB,EAAAA,SAAQ,gBAAgB;AACxB,EAAAA,SAAQ,kBAAkB;AAC1B,EAAAA,SAAQ,kBAAkB;AAC1B,EAAAA,SAAQ,kBAAkB;AAC1B,EAAAA,SAAQ,mBAAmB;AAC3B,EAAAA,SAAQ,SAAS;AAEjB,EAAAA,SAAQ,SAASA,SAAQ,MAAM;AAC/B,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,YAAY;AAEpB,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,KAAK;AACb,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,SAAS;AACjB,EAAAA,SAAQ,QAAQ;AAChB,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,MAAM;AACd,EAAAA,SAAQ,OAAO;AACf,EAAAA,SAAQ,QAAQ;AAEhB,MAAI,QAAQ;AAAQ,UAAM,CAAC;AAC3B,MAAI,KAAK;AACP,QAAI,IAAI,aAAa,MAAM;AACzB,WAAK,CAAC,aAAa,YAAY,YAAY,YAAY,QAAQ,QAAQ,UAAU,QAAQ;AACzF,WAAK,IAAI,GAAG,IAAI,GAAG;AAAS,YAAI,CAAC,IAAI,eAAe,IAAI,GAAG,GAAG,CAAC;AAAG,cAAI,CAAC,IAAI,KAAK,CAAC;AAAA,IACnF;AAAA,EACF;AAEA,EAAAA,SAAQ,OAAO,GAAG;AAElB,SAAOA;AACT;AAWA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1B;AAUA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI;AACzB;AASA,SAAS,MAAM,GAAG;AAChB,SAAO,SAAS,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC7C;AAYA,SAAS,QAAQ;AACf,MAAI,GAAG,GACL,IAAI,IAAI,KAAK,CAAC;AAEhB,aAAW;AAEX,OAAK,IAAI,GAAG,IAAI,UAAU,UAAS;AACjC,QAAI,IAAI,KAAK,UAAU,GAAG,CAAC;AAC3B,QAAI,CAAC,EAAE,GAAG;AACR,UAAI,EAAE,GAAG;AACP,mBAAW;AACX,eAAO,IAAI,KAAK,IAAI,CAAC;AAAA,MACvB;AACA,UAAI;AAAA,IACN,WAAW,EAAE,GAAG;AACd,UAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAAA,IACvB;AAAA,EACF;AAEA,aAAW;AAEX,SAAO,EAAE,KAAK;AAChB;AAQA,SAAS,kBAAkB,KAAK;AAC9B,SAAO,eAAe,WAAW,OAAO,IAAI,gBAAgB,OAAO;AACrE;AAUA,SAAS,GAAG,GAAG;AACb,SAAO,IAAI,KAAK,CAAC,EAAE,GAAG;AACxB;AAaA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1B;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1B;AAUA,SAAS,MAAM,GAAG;AAChB,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,EAAE;AAC3B;AASA,SAAS,MAAM;AACb,SAAO,SAAS,MAAM,WAAW,EAAE;AACrC;AASA,SAAS,MAAM;AACb,SAAO,SAAS,MAAM,WAAW,CAAC;AACpC;AAWA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1B;AAWA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1B;AAWA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1B;AAWA,SAAS,OAAO,IAAI;AAClB,MAAI,GAAG,GAAG,GAAG,GACX,IAAI,GACJ,IAAI,IAAI,KAAK,CAAC,GACd,KAAK,CAAC;AAER,MAAI,OAAO;AAAQ,SAAK,KAAK;AAAA;AACxB,eAAW,IAAI,GAAG,UAAU;AAEjC,MAAI,KAAK,KAAK,KAAK,QAAQ;AAE3B,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO,IAAI;AAAI,SAAG,GAAG,IAAI,KAAK,OAAO,IAAI,MAAM;AAAA,EAGjD,WAAW,OAAO,iBAAiB;AACjC,QAAI,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC;AAE7C,WAAO,IAAI,KAAI;AACb,UAAI,EAAE,CAAC;AAIP,UAAI,KAAK,OAAQ;AACf,UAAE,CAAC,IAAI,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC;AAAA,MACrD,OAAO;AAIL,WAAG,GAAG,IAAI,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EAGF,WAAW,OAAO,aAAa;AAG7B,QAAI,OAAO,YAAY,KAAK,CAAC;AAE7B,WAAO,IAAI,KAAI;AAGb,UAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,MAAM,EAAE,IAAI,CAAC,KAAK,QAAQ,EAAE,IAAI,CAAC,IAAI,QAAS;AAGtE,UAAI,KAAK,OAAQ;AACf,eAAO,YAAY,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,MACjC,OAAO;AAIL,WAAG,KAAK,IAAI,GAAG;AACf,aAAK;AAAA,MACP;AAAA,IACF;AAEA,QAAI,IAAI;AAAA,EACV,OAAO;AACL,UAAM,MAAM,iBAAiB;AAAA,EAC/B;AAEA,MAAI,GAAG,EAAE,CAAC;AACV,QAAM;AAGN,MAAI,KAAK,IAAI;AACX,QAAI,QAAQ,IAAI,WAAW,EAAE;AAC7B,OAAG,CAAC,KAAK,IAAI,IAAI,KAAK;AAAA,EACxB;AAGA,SAAO,GAAG,CAAC,MAAM,GAAG;AAAK,OAAG,IAAI;AAGhC,MAAI,IAAI,GAAG;AACT,QAAI;AACJ,SAAK,CAAC,CAAC;AAAA,EACT,OAAO;AACL,QAAI;AAGJ,WAAO,GAAG,CAAC,MAAM,GAAG,KAAK;AAAU,SAAG,MAAM;AAG5C,SAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK;AAAI;AAGzC,QAAI,IAAI;AAAU,WAAK,WAAW;AAAA,EACpC;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAWA,SAAS,MAAM,GAAG;AAChB,SAAO,SAAS,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,KAAK,QAAQ;AACzD;AAcA,SAAS,KAAK,GAAG;AACf,MAAI,IAAI,KAAK,CAAC;AACd,SAAO,EAAE,IAAK,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,IAAK,EAAE,KAAK;AACjD;AAUA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI;AACzB;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AAWA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1B;AAYA,SAAS,MAAM;AACb,MAAI,IAAI,GACN,OAAO,WACP,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC;AAEtB,aAAW;AACX,SAAO,EAAE,KAAK,EAAE,IAAI,KAAK;AAAS,QAAI,EAAE,KAAK,KAAK,CAAC,CAAC;AACpD,aAAW;AAEX,SAAO,SAAS,GAAG,KAAK,WAAW,KAAK,QAAQ;AAClD;AAUA,SAAS,IAAI,GAAG;AACd,SAAO,IAAI,KAAK,CAAC,EAAE,IAAI;AACzB;AAUA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC,EAAE,KAAK;AAC1B;AASA,SAAS,MAAM,GAAG;AAChB,SAAO,SAAS,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC7C;AAGA,EAAE,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE;AAChD,EAAE,OAAO,WAAW,IAAI;AAGjB,IAAI,UAAU,EAAE,cAAc,MAAM,QAAQ;AAGnD,OAAO,IAAI,QAAQ,IAAI;AACvB,KAAK,IAAI,QAAQ,EAAE;AAEnB,IAAO,kBAAQ;", "names": ["min", "max", "sign", "pow", "sum", "Decimal", "i"]}