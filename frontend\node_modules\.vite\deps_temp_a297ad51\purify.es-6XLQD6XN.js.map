{"version": 3, "sources": ["../../dompurify/src/utils.js", "../../dompurify/src/tags.js", "../../dompurify/src/attrs.js", "../../dompurify/src/regexp.js", "../../dompurify/src/purify.js"], "sourcesContent": ["const {\n  hasOwnProperty,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array, transformCaseFunc) {\n  transformCaseFunc = transformCaseFunc ?? stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = create(null);\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property]) === true) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  hasOwnProperty,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'fedropshadow',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n} from './utils.js';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined &&\n    documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n        : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES =\n      'ALLOWED_NAMESPACES' in cfg\n        ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n        : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(\n            clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n            cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(\n            clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n            cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS =\n      'FORBID_TAGS' in cfg\n        ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n        : {};\n    FORBID_ATTR =\n      'FORBID_ATTR' in cfg\n        ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n        : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null,\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      (!_isNode(currentNode.content) ||\n        !_isNode(currentNode.content.firstElementChild)) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (\n      tagName === 'select' &&\n      regExpTest(/<template/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any ocurrence of processing instructions */\n    if (currentNode.nodeType === 7) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === 8 &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        )\n          return false;\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        )\n          return false;\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_basicCustomElementTest(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  const _basicCustomElementTest = function (tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IACEA,iBAKEC,OALFD;AADF,IAEEE,iBAIED,OAJFC;AAFF,IAGEC,WAGEF,OAHFE;AAHF,IAIEC,iBAEEH,OAFFG;AAJF,IAKEC,2BACEJ,OADFI;AAGF,IAAMC,SAAyBL,OAAzBK;AAAN,IAAcC,OAAiBN,OAAjBM;AAAd,IAAoBC,SAAWP,OAAXO;AACpB,IAAAC,OAA2B,OAAOC,YAAY,eAAeA;AAA7D,IAAMC,QAAKF,KAALE;AAAN,IAAaC,YAASH,KAATG;AAEb,IAAI,CAACD,OAAO;AACVA,UAAQ,SAAAA,OAAUE,KAAKC,WAAWC,MAAM;AACtC,WAAOF,IAAIF,MAAMG,WAAWC,IAAI;;AAEpC;AAEA,IAAI,CAACT,QAAQ;AACXA,WAAS,SAAAA,QAAUU,GAAG;AACpB,WAAOA;;AAEX;AAEA,IAAI,CAACT,MAAM;AACTA,SAAO,SAAAA,MAAUS,GAAG;AAClB,WAAOA;;AAEX;AAEA,IAAI,CAACJ,WAAW;AACdA,cAAY,SAAAA,WAAUK,MAAMF,MAAM;AAChC,WAAAG,WAAWD,MAAIE,mBAAIJ,IAAI,CAAA;;AAE3B;AAEA,IAAMK,eAAeC,QAAQC,MAAMC,UAAUC,OAAO;AAEpD,IAAMC,WAAWJ,QAAQC,MAAMC,UAAUG,GAAG;AAC5C,IAAMC,YAAYN,QAAQC,MAAMC,UAAUK,IAAI;AAG9C,IAAMC,oBAAoBR,QAAQS,OAAOP,UAAUQ,WAAW;AAC9D,IAAMC,iBAAiBX,QAAQS,OAAOP,UAAUU,QAAQ;AACxD,IAAMC,cAAcb,QAAQS,OAAOP,UAAUY,KAAK;AAClD,IAAMC,gBAAgBf,QAAQS,OAAOP,UAAUc,OAAO;AACtD,IAAMC,gBAAgBjB,QAAQS,OAAOP,UAAUgB,OAAO;AACtD,IAAMC,aAAanB,QAAQS,OAAOP,UAAUkB,IAAI;AAEhD,IAAMC,aAAarB,QAAQsB,OAAOpB,UAAUqB,IAAI;AAEhD,IAAMC,kBAAkBC,YAAYC,SAAS;AAEtC,SAAS1B,QAAQ2B,MAAM;AAC5B,SAAO,SAACC,SAAO;AAAA,aAAAC,OAAAC,UAAAC,QAAKrC,OAAI,IAAAO,MAAA4B,OAAAA,IAAAA,OAAA,IAAA,CAAA,GAAAG,OAAA,GAAAA,OAAAH,MAAAG,QAAA;AAAJtC,WAAIsC,OAAAF,CAAAA,IAAAA,UAAAE,IAAA;IAAA;AAAA,WAAK1C,MAAMqC,MAAMC,SAASlC,IAAI;EAAC;AACzD;AAEO,SAAS+B,YAAYE,MAAM;AAChC,SAAO,WAAA;AAAA,aAAAM,QAAAH,UAAAC,QAAIrC,OAAIO,IAAAA,MAAAgC,KAAA,GAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAA;AAAJxC,WAAIwC,KAAA,IAAAJ,UAAAI,KAAA;IAAA;AAAA,WAAK3C,UAAUoC,MAAMjC,IAAI;EAAC;AAC3C;AAGO,SAASyC,SAASC,KAAKC,OAAOC,mBAAmB;AAAA,MAAAC;AACtDD,uBAAiBC,qBAAGD,uBAAiB,QAAAC,uBAAA,SAAAA,qBAAI/B;AACzC,MAAI3B,gBAAgB;AAIlBA,mBAAeuD,KAAK,IAAI;EAC1B;AAEA,MAAII,IAAIH,MAAMN;AACd,SAAOS,KAAK;AACV,QAAIC,UAAUJ,MAAMG,CAAC;AACrB,QAAI,OAAOC,YAAY,UAAU;AAC/B,UAAMC,YAAYJ,kBAAkBG,OAAO;AAC3C,UAAIC,cAAcD,SAAS;AAEzB,YAAI,CAAC3D,SAASuD,KAAK,GAAG;AACpBA,gBAAMG,CAAC,IAAIE;QACb;AAEAD,kBAAUC;MACZ;IACF;AAEAN,QAAIK,OAAO,IAAI;EACjB;AAEA,SAAOL;AACT;AAGO,SAASO,MAAMC,QAAQ;AAC5B,MAAMC,YAAY1D,OAAO,IAAI;AAE7B,MAAI2D;AACJ,OAAKA,YAAYF,QAAQ;AACvB,QAAItD,MAAMX,gBAAgBiE,QAAQ,CAACE,QAAQ,CAAC,MAAM,MAAM;AACtDD,gBAAUC,QAAQ,IAAIF,OAAOE,QAAQ;IACvC;EACF;AAEA,SAAOD;AACT;AAMA,SAASE,aAAaH,QAAQI,MAAM;AAClC,SAAOJ,WAAW,MAAM;AACtB,QAAMK,OAAOjE,yBAAyB4D,QAAQI,IAAI;AAClD,QAAIC,MAAM;AACR,UAAIA,KAAKC,KAAK;AACZ,eAAOlD,QAAQiD,KAAKC,GAAG;MACzB;AAEA,UAAI,OAAOD,KAAKE,UAAU,YAAY;AACpC,eAAOnD,QAAQiD,KAAKE,KAAK;MAC3B;IACF;AAEAP,aAAS7D,eAAe6D,MAAM;EAChC;AAEA,WAASQ,cAAcX,SAAS;AAC9BY,YAAQC,KAAK,sBAAsBb,OAAO;AAC1C,WAAO;EACT;AAEA,SAAOW;AACT;ACjIO,IAAMG,SAAOtE,OAAO,CACzB,KACA,QACA,WACA,WACA,QACA,WACA,SACA,SACA,KACA,OACA,OACA,OACA,SACA,cACA,QACA,MACA,UACA,UACA,WACA,UACA,QACA,QACA,OACA,YACA,WACA,QACA,YACA,MACA,aACA,OACA,WACA,OACA,UACA,OACA,OACA,MACA,MACA,WACA,MACA,YACA,cACA,UACA,QACA,UACA,QACA,MACA,MACA,MACA,MACA,MACA,MACA,QACA,UACA,UACA,MACA,QACA,KACA,OACA,SACA,OACA,OACA,SACA,UACA,MACA,QACA,OACA,QACA,WACA,QACA,YACA,SACA,OACA,QACA,MACA,YACA,UACA,UACA,KACA,WACA,OACA,YACA,KACA,MACA,MACA,QACA,KACA,QACA,WACA,UACA,UACA,SACA,UACA,UACA,QACA,UACA,UACA,SACA,OACA,WACA,OACA,SACA,SACA,MACA,YACA,YACA,SACA,MACA,SACA,QACA,MACA,SACA,MACA,KACA,MACA,OACA,SACA,KAAK,CACN;AAGM,IAAMuE,QAAMvE,OAAO,CACxB,OACA,KACA,YACA,eACA,gBACA,gBACA,iBACA,oBACA,UACA,YACA,QACA,QACA,WACA,UACA,QACA,KACA,SACA,YACA,SACA,SACA,QACA,kBACA,UACA,QACA,YACA,SACA,QACA,WACA,WACA,YACA,kBACA,QACA,QACA,SACA,UACA,UACA,QACA,YACA,SACA,QACA,SACA,QACA,OAAO,CACR;AAEM,IAAMwE,aAAaxE,OAAO,CAC/B,WACA,iBACA,uBACA,eACA,oBACA,qBACA,qBACA,kBACA,WACA,WACA,WACA,WACA,WACA,kBACA,WACA,WACA,eACA,gBACA,YACA,gBACA,sBACA,eACA,UACA,cAAc,CACf;AAMM,IAAMyE,gBAAgBzE,OAAO,CAClC,WACA,iBACA,UACA,WACA,gBACA,aACA,oBACA,kBACA,iBACA,iBACA,iBACA,SACA,aACA,QACA,gBACA,aACA,WACA,iBACA,UACA,OACA,cACA,WACA,KAAK,CACN;AAEM,IAAM0E,WAAS1E,OAAO,CAC3B,QACA,YACA,UACA,WACA,SACA,UACA,MACA,cACA,iBACA,MACA,MACA,SACA,WACA,YACA,SACA,QACA,MACA,UACA,SACA,UACA,QACA,QACA,WACA,UACA,OACA,SACA,OACA,UACA,YAAY,CACb;AAIM,IAAM2E,mBAAmB3E,OAAO,CACrC,WACA,eACA,cACA,YACA,aACA,WACA,WACA,UACA,UACA,SACA,aACA,cACA,kBACA,eACA,MAAM,CACP;AAEM,IAAM4E,OAAO5E,OAAO,CAAC,OAAO,CAAC;ACpR7B,IAAMsE,OAAOtE,OAAO,CACzB,UACA,UACA,SACA,OACA,kBACA,gBACA,wBACA,YACA,cACA,WACA,UACA,WACA,eACA,eACA,WACA,QACA,SACA,SACA,SACA,QACA,WACA,YACA,gBACA,UACA,eACA,YACA,YACA,WACA,OACA,YACA,2BACA,yBACA,YACA,aACA,WACA,gBACA,QACA,OACA,WACA,UACA,UACA,QACA,QACA,YACA,MACA,aACA,aACA,SACA,QACA,SACA,QACA,QACA,WACA,QACA,OACA,OACA,aACA,SACA,UACA,OACA,aACA,YACA,SACA,QACA,SACA,WACA,cACA,UACA,QACA,WACA,WACA,eACA,eACA,UACA,WACA,WACA,cACA,YACA,OACA,YACA,OACA,YACA,QACA,QACA,WACA,cACA,SACA,YACA,SACA,QACA,SACA,QACA,WACA,SACA,OACA,UACA,QACA,SACA,WACA,YACA,SACA,aACA,QACA,UACA,UACA,SACA,SACA,SACA,MAAM,CACP;AAEM,IAAMuE,MAAMvE,OAAO,CACxB,iBACA,cACA,YACA,sBACA,UACA,iBACA,iBACA,WACA,iBACA,kBACA,SACA,QACA,MACA,SACA,QACA,iBACA,aACA,aACA,SACA,uBACA,+BACA,iBACA,mBACA,MACA,MACA,KACA,MACA,MACA,mBACA,aACA,WACA,WACA,OACA,YACA,aACA,OACA,QACA,gBACA,aACA,UACA,eACA,eACA,iBACA,eACA,aACA,oBACA,gBACA,cACA,gBACA,eACA,MACA,MACA,MACA,MACA,cACA,YACA,iBACA,qBACA,UACA,QACA,MACA,mBACA,MACA,OACA,KACA,MACA,MACA,MACA,MACA,WACA,aACA,cACA,YACA,QACA,gBACA,kBACA,gBACA,oBACA,kBACA,SACA,cACA,cACA,gBACA,gBACA,eACA,eACA,oBACA,aACA,OACA,QACA,SACA,UACA,QACA,OACA,QACA,cACA,UACA,YACA,WACA,SACA,UACA,eACA,UACA,YACA,eACA,QACA,cACA,uBACA,oBACA,gBACA,UACA,iBACA,uBACA,kBACA,KACA,MACA,MACA,UACA,QACA,QACA,eACA,aACA,WACA,UACA,UACA,SACA,QACA,mBACA,oBACA,oBACA,gBACA,eACA,gBACA,eACA,cACA,gBACA,oBACA,qBACA,kBACA,mBACA,qBACA,kBACA,UACA,gBACA,SACA,gBACA,kBACA,YACA,WACA,WACA,aACA,oBACA,eACA,mBACA,kBACA,cACA,QACA,MACA,MACA,WACA,UACA,WACA,cACA,WACA,cACA,iBACA,iBACA,SACA,gBACA,QACA,gBACA,oBACA,oBACA,KACA,MACA,MACA,SACA,KACA,MACA,MACA,KACA,YAAY,CACb;AAEM,IAAM0E,SAAS1E,OAAO,CAC3B,UACA,eACA,SACA,YACA,SACA,gBACA,eACA,cACA,cACA,SACA,OACA,WACA,gBACA,YACA,SACA,SACA,UACA,QACA,MACA,WACA,UACA,iBACA,UACA,UACA,kBACA,aACA,YACA,eACA,WACA,WACA,iBACA,YACA,YACA,QACA,YACA,YACA,cACA,WACA,UACA,UACA,eACA,iBACA,wBACA,aACA,aACA,cACA,YACA,kBACA,kBACA,aACA,WACA,SACA,OAAO,CACR;AAEM,IAAM6E,MAAM7E,OAAO,CACxB,cACA,UACA,eACA,aACA,aAAa,CACd;ACtWM,IAAM8E,gBAAgB7E,KAAK,2BAA2B;AACtD,IAAM8E,WAAW9E,KAAK,uBAAuB;AAC7C,IAAM+E,cAAc/E,KAAK,eAAe;AACxC,IAAMgF,YAAYhF,KAAK,4BAA4B;AACnD,IAAMiF,YAAYjF,KAAK,gBAAgB;AACvC,IAAMkF,iBAAiBlF;EAC5B;;AACF;AACO,IAAMmF,oBAAoBnF,KAAK,uBAAuB;AACtD,IAAMoF,kBAAkBpF;EAC7B;;AACF;AACO,IAAMqF,eAAerF,KAAK,SAAS;AACnC,IAAMsF,iBAAiBtF,KAAK,0BAA0B;ACK7D,IAAMuF,YAAY,SAAZA,aAAS;AAAA,SAAU,OAAOC,WAAW,cAAc,OAAOA;AAAM;AAUtE,IAAMC,4BAA4B,SAA5BA,2BAAsCC,cAAcC,UAAU;AAClE,MACEC,QAAOF,YAAY,MAAK,YACxB,OAAOA,aAAaG,iBAAiB,YACrC;AACA,WAAO;EACT;AAKA,MAAIC,SAAS;AACb,MAAMC,YAAY;AAClB,MACEJ,SAASK,iBACTL,SAASK,cAAcC,aAAaF,SAAS,GAC7C;AACAD,aAASH,SAASK,cAAcE,aAAaH,SAAS;EACxD;AAEA,MAAMI,aAAa,eAAeL,SAAS,MAAMA,SAAS;AAE1D,MAAI;AACF,WAAOJ,aAAaG,aAAaM,YAAY;MAC3CC,YAAU,SAAAA,WAAC/B,OAAM;AACf,eAAOA;;MAETgC,iBAAe,SAAAA,gBAACC,WAAW;AACzB,eAAOA;MACT;IACF,CAAC;WACMC,GAAG;AAIVpC,YAAQC,KACN,yBAAyB+B,aAAa,wBACxC;AACA,WAAO;EACT;AACF;AAEA,SAASK,kBAAsC;AAAA,MAAtBhB,UAAM5C,UAAAC,SAAAD,KAAAA,UAAA6D,CAAAA,MAAAA,SAAA7D,UAAG2C,CAAAA,IAAAA,UAAS;AACzC,MAAMmB,YAAY,SAAZA,WAAaC,MAAI;AAAA,WAAKH,gBAAgBG,IAAI;EAAC;AAMjDD,YAAUE,UAAUC;AAMpBH,YAAUI,UAAU,CAAA;AAEpB,MAAI,CAACtB,WAAU,CAACA,QAAOG,YAAYH,QAAOG,SAASoB,aAAa,GAAG;AAGjEL,cAAUM,cAAc;AAExB,WAAON;EACT;AAEA,MAAMO,mBAAmBzB,QAAOG;AAEhC,MAAMA,WAAaH,QAAbG;AACN,MACEuB,mBASE1B,QATF0B,kBACAC,sBAQE3B,QARF2B,qBACAC,OAOE5B,QAPF4B,MACAC,UAME7B,QANF6B,SACAC,aAKE9B,QALF8B,YAAUC,uBAKR/B,QAJFgC,cAAAA,eAAYD,yBAAA,SAAG/B,QAAOgC,gBAAgBhC,QAAOiC,kBAAeF,sBAC5DG,kBAGElC,QAHFkC,iBACAC,YAEEnC,QAFFmC,WACAjC,eACEF,QADFE;AAGF,MAAMkC,mBAAmBP,QAAQrG;AAEjC,MAAM6G,YAAYhE,aAAa+D,kBAAkB,WAAW;AAC5D,MAAME,iBAAiBjE,aAAa+D,kBAAkB,aAAa;AACnE,MAAMG,gBAAgBlE,aAAa+D,kBAAkB,YAAY;AACjE,MAAMI,gBAAgBnE,aAAa+D,kBAAkB,YAAY;AAQjE,MAAI,OAAOT,wBAAwB,YAAY;AAC7C,QAAMc,WAAWtC,SAASuC,cAAc,UAAU;AAClD,QAAID,SAASE,WAAWF,SAASE,QAAQC,eAAe;AACtDzC,iBAAWsC,SAASE,QAAQC;IAC9B;EACF;AAEA,MAAMC,qBAAqB5C,0BACzBC,cACAuB,gBACF;AACA,MAAMqB,YAAYD,qBAAqBA,mBAAmBjC,WAAW,EAAE,IAAI;AAE3E,MAAAmC,YAKI5C,UAJF6C,iBAAcD,UAAdC,gBACAC,qBAAkBF,UAAlBE,oBACAC,yBAAsBH,UAAtBG,wBACAC,uBAAoBJ,UAApBI;AAEF,MAAQC,aAAe3B,iBAAf2B;AAER,MAAIC,eAAe,CAAA;AACnB,MAAI;AACFA,mBAAepF,MAAMkC,QAAQ,EAAEkD,eAAelD,SAASkD,eAAe,CAAA;EACxE,SAAStC,GAAG;EAAA;AAEZ,MAAIuC,QAAQ,CAAA;AAKZpC,YAAUM,cACR,OAAOgB,kBAAkB,cACzBQ,kBACAA,eAAeO,uBAAuBtC,UACtCoC,iBAAiB;AAEnB,MACEhE,kBAQEmE,eAPFlE,aAOEkE,UANFjE,gBAMEiE,aALFhE,cAKEgE,WAJF/D,cAIE+D,WAHF7D,sBAGE6D,mBAFF5D,oBAEE4D,iBADF1D,mBACE0D;AAEJ,MAAM9D,mBAAmB8D;AAQzB,MAAIC,eAAe;AACnB,MAAMC,uBAAuBjG,SAAS,CAAA,GAAEkG,CAAAA,EAAAA,OAAAvI,mBACnCwI,MAAS,GAAAxI,mBACTwI,KAAQ,GAAAxI,mBACRwI,UAAe,GAAAxI,mBACfwI,QAAW,GAAAxI,mBACXwI,IAAS,CAAA,CACb;AAGD,MAAIC,eAAe;AACnB,MAAMC,uBAAuBrG,SAAS,CAAA,GAAE,CAAA,EAAAkG,OAAAvI,mBACnC2I,IAAU,GAAA3I,mBACV2I,GAAS,GAAA3I,mBACT2I,MAAY,GAAA3I,mBACZ2I,GAAS,CAAA,CACb;AAQD,MAAIC,0BAA0B9J,OAAOM,KACnCN,OAAOO,OAAO,MAAM;IAClBwJ,cAAc;MACZC,UAAU;MACVC,cAAc;MACdC,YAAY;MACZ3F,OAAO;;IAET4F,oBAAoB;MAClBH,UAAU;MACVC,cAAc;MACdC,YAAY;MACZ3F,OAAO;;IAET6F,gCAAgC;MAC9BJ,UAAU;MACVC,cAAc;MACdC,YAAY;MACZ3F,OAAO;IACT;EACF,CAAC,CACH;AAGA,MAAI8F,cAAc;AAGlB,MAAIC,cAAc;AAGlB,MAAIC,kBAAkB;AAGtB,MAAIC,kBAAkB;AAGtB,MAAIC,0BAA0B;AAI9B,MAAIC,2BAA2B;AAK/B,MAAIC,qBAAqB;AAKzB,MAAIC,eAAe;AAGnB,MAAIC,iBAAiB;AAGrB,MAAIC,aAAa;AAIjB,MAAIC,aAAa;AAMjB,MAAIC,aAAa;AAIjB,MAAIC,sBAAsB;AAI1B,MAAIC,sBAAsB;AAK1B,MAAIC,eAAe;AAenB,MAAIC,uBAAuB;AAC3B,MAAMC,8BAA8B;AAGpC,MAAIC,eAAe;AAInB,MAAIC,WAAW;AAGf,MAAIC,eAAe,CAAA;AAGnB,MAAIC,kBAAkB;AACtB,MAAMC,0BAA0BnI,SAAS,CAAA,GAAI,CAC3C,kBACA,SACA,YACA,QACA,iBACA,QACA,UACA,QACA,MACA,MACA,MACA,MACA,SACA,WACA,YACA,YACA,aACA,UACA,SACA,OACA,YACA,SACA,SACA,SACA,KAAK,CACN;AAGD,MAAIoI,gBAAgB;AACpB,MAAMC,wBAAwBrI,SAAS,CAAA,GAAI,CACzC,SACA,SACA,OACA,UACA,SACA,OAAO,CACR;AAGD,MAAIsI,sBAAsB;AAC1B,MAAMC,8BAA8BvI,SAAS,CAAA,GAAI,CAC/C,OACA,SACA,OACA,MACA,SACA,QACA,WACA,eACA,QACA,WACA,SACA,SACA,SACA,OAAO,CACR;AAED,MAAMwI,mBAAmB;AACzB,MAAMC,gBAAgB;AACtB,MAAMC,iBAAiB;AAEvB,MAAIC,YAAYD;AAChB,MAAIE,iBAAiB;AAGrB,MAAIC,qBAAqB;AACzB,MAAMC,6BAA6B9I,SACjC,CAAA,GACA,CAACwI,kBAAkBC,eAAeC,cAAc,GAChDlK,cACF;AAGA,MAAIuK;AACJ,MAAMC,+BAA+B,CAAC,yBAAyB,WAAW;AAC1E,MAAMC,4BAA4B;AAClC,MAAI9I;AAGJ,MAAI+I,SAAS;AAKb,MAAMC,cAAczG,SAASuC,cAAc,MAAM;AAEjD,MAAMmE,oBAAoB,SAApBA,mBAA8BC,WAAW;AAC7C,WAAOA,qBAAqBlK,UAAUkK,qBAAqBC;;AAS7D,MAAMC,eAAe,SAAfA,cAAyBC,KAAK;AAClC,QAAIN,UAAUA,WAAWM,KAAK;AAC5B;IACF;AAGA,QAAI,CAACA,OAAO7G,QAAO6G,GAAG,MAAK,UAAU;AACnCA,YAAM,CAAA;IACR;AAGAA,UAAMhJ,MAAMgJ,GAAG;AAEfT;IAEEC,6BAA6BjK,QAAQyK,IAAIT,iBAAiB,MAAM,KAC3DA,oBAAoBE,4BACpBF,oBAAoBS,IAAIT;AAG/B5I,wBACE4I,sBAAsB,0BAClBvK,iBACAH;AAGN2H,mBACE,kBAAkBwD,MACdxJ,SAAS,CAAA,GAAIwJ,IAAIxD,cAAc7F,iBAAiB,IAChD8F;AACNG,mBACE,kBAAkBoD,MACdxJ,SAAS,CAAA,GAAIwJ,IAAIpD,cAAcjG,iBAAiB,IAChDkG;AACNwC,yBACE,wBAAwBW,MACpBxJ,SAAS,CAAA,GAAIwJ,IAAIX,oBAAoBrK,cAAc,IACnDsK;AACNR,0BACE,uBAAuBkB,MACnBxJ;MACEQ,MAAM+H,2BAA2B;;MACjCiB,IAAIC;;MACJtJ;;IACF,IACAoI;AACNH,oBACE,uBAAuBoB,MACnBxJ;MACEQ,MAAM6H,qBAAqB;;MAC3BmB,IAAIE;;MACJvJ;;IACF,IACAkI;AACNH,sBACE,qBAAqBsB,MACjBxJ,SAAS,CAAA,GAAIwJ,IAAItB,iBAAiB/H,iBAAiB,IACnDgI;AACNrB,kBACE,iBAAiB0C,MACbxJ,SAAS,CAAA,GAAIwJ,IAAI1C,aAAa3G,iBAAiB,IAC/C,CAAA;AACN4G,kBACE,iBAAiByC,MACbxJ,SAAS,CAAA,GAAIwJ,IAAIzC,aAAa5G,iBAAiB,IAC/C,CAAA;AACN8H,mBAAe,kBAAkBuB,MAAMA,IAAIvB,eAAe;AAC1DjB,sBAAkBwC,IAAIxC,oBAAoB;AAC1CC,sBAAkBuC,IAAIvC,oBAAoB;AAC1CC,8BAA0BsC,IAAItC,2BAA2B;AACzDC,+BAA2BqC,IAAIrC,6BAA6B;AAC5DC,yBAAqBoC,IAAIpC,sBAAsB;AAC/CC,mBAAemC,IAAInC,iBAAiB;AACpCC,qBAAiBkC,IAAIlC,kBAAkB;AACvCG,iBAAa+B,IAAI/B,cAAc;AAC/BC,0BAAsB8B,IAAI9B,uBAAuB;AACjDC,0BAAsB6B,IAAI7B,uBAAuB;AACjDH,iBAAagC,IAAIhC,cAAc;AAC/BI,mBAAe4B,IAAI5B,iBAAiB;AACpCC,2BAAuB2B,IAAI3B,wBAAwB;AACnDE,mBAAeyB,IAAIzB,iBAAiB;AACpCC,eAAWwB,IAAIxB,YAAY;AAC3B/F,uBAAiBuH,IAAIG,sBAAsB1H;AAC3C0G,gBAAYa,IAAIb,aAAaD;AAC7BnC,8BAA0BiD,IAAIjD,2BAA2B,CAAA;AACzD,QACEiD,IAAIjD,2BACJ6C,kBAAkBI,IAAIjD,wBAAwBC,YAAY,GAC1D;AACAD,8BAAwBC,eACtBgD,IAAIjD,wBAAwBC;IAChC;AAEA,QACEgD,IAAIjD,2BACJ6C,kBAAkBI,IAAIjD,wBAAwBK,kBAAkB,GAChE;AACAL,8BAAwBK,qBACtB4C,IAAIjD,wBAAwBK;IAChC;AAEA,QACE4C,IAAIjD,2BACJ,OAAOiD,IAAIjD,wBAAwBM,mCACjC,WACF;AACAN,8BAAwBM,iCACtB2C,IAAIjD,wBAAwBM;IAChC;AAEA,QAAIO,oBAAoB;AACtBH,wBAAkB;IACpB;AAEA,QAAIS,qBAAqB;AACvBD,mBAAa;IACf;AAGA,QAAIQ,cAAc;AAChBjC,qBAAehG,SAAS,CAAA,GAAErC,mBAAMwI,IAAS,CAAC;AAC1CC,qBAAe,CAAA;AACf,UAAI6B,aAAa7G,SAAS,MAAM;AAC9BpB,iBAASgG,cAAcG,MAAS;AAChCnG,iBAASoG,cAAcE,IAAU;MACnC;AAEA,UAAI2B,aAAa5G,QAAQ,MAAM;AAC7BrB,iBAASgG,cAAcG,KAAQ;AAC/BnG,iBAASoG,cAAcE,GAAS;AAChCtG,iBAASoG,cAAcE,GAAS;MAClC;AAEA,UAAI2B,aAAa3G,eAAe,MAAM;AACpCtB,iBAASgG,cAAcG,UAAe;AACtCnG,iBAASoG,cAAcE,GAAS;AAChCtG,iBAASoG,cAAcE,GAAS;MAClC;AAEA,UAAI2B,aAAazG,WAAW,MAAM;AAChCxB,iBAASgG,cAAcG,QAAW;AAClCnG,iBAASoG,cAAcE,MAAY;AACnCtG,iBAASoG,cAAcE,GAAS;MAClC;IACF;AAGA,QAAIkD,IAAII,UAAU;AAChB,UAAI5D,iBAAiBC,sBAAsB;AACzCD,uBAAexF,MAAMwF,YAAY;MACnC;AAEAhG,eAASgG,cAAcwD,IAAII,UAAUzJ,iBAAiB;IACxD;AAEA,QAAIqJ,IAAIK,UAAU;AAChB,UAAIzD,iBAAiBC,sBAAsB;AACzCD,uBAAe5F,MAAM4F,YAAY;MACnC;AAEApG,eAASoG,cAAcoD,IAAIK,UAAU1J,iBAAiB;IACxD;AAEA,QAAIqJ,IAAIC,mBAAmB;AACzBzJ,eAASsI,qBAAqBkB,IAAIC,mBAAmBtJ,iBAAiB;IACxE;AAEA,QAAIqJ,IAAItB,iBAAiB;AACvB,UAAIA,oBAAoBC,yBAAyB;AAC/CD,0BAAkB1H,MAAM0H,eAAe;MACzC;AAEAlI,eAASkI,iBAAiBsB,IAAItB,iBAAiB/H,iBAAiB;IAClE;AAGA,QAAI4H,cAAc;AAChB/B,mBAAa,OAAO,IAAI;IAC1B;AAGA,QAAIsB,gBAAgB;AAClBtH,eAASgG,cAAc,CAAC,QAAQ,QAAQ,MAAM,CAAC;IACjD;AAGA,QAAIA,aAAa8D,OAAO;AACtB9J,eAASgG,cAAc,CAAC,OAAO,CAAC;AAChC,aAAOc,YAAYiD;IACrB;AAIA,QAAIjN,QAAQ;AACVA,aAAO0M,GAAG;IACZ;AAEAN,aAASM;;AAGX,MAAMQ,iCAAiChK,SAAS,CAAA,GAAI,CAClD,MACA,MACA,MACA,MACA,OAAO,CACR;AAED,MAAMiK,0BAA0BjK,SAAS,CAAA,GAAI,CAAC,gBAAgB,CAAC;AAM/D,MAAMkK,+BAA+BlK,SAAS,CAAA,GAAI,CAChD,SACA,SACA,QACA,KACA,QAAQ,CACT;AAKD,MAAMmK,eAAenK,SAAS,CAAA,GAAImG,KAAQ;AAC1CnG,WAASmK,cAAchE,UAAe;AACtCnG,WAASmK,cAAchE,aAAkB;AAEzC,MAAMiE,kBAAkBpK,SAAS,CAAA,GAAImG,QAAW;AAChDnG,WAASoK,iBAAiBjE,gBAAqB;AAU/C,MAAMkE,uBAAuB,SAAvBA,sBAAiC/J,SAAS;AAC9C,QAAIgK,SAASvF,cAAczE,OAAO;AAIlC,QAAI,CAACgK,UAAU,CAACA,OAAOC,SAAS;AAC9BD,eAAS;QACPE,cAAc7B;QACd4B,SAAS;;IAEb;AAEA,QAAMA,UAAUlM,kBAAkBiC,QAAQiK,OAAO;AACjD,QAAME,gBAAgBpM,kBAAkBiM,OAAOC,OAAO;AAEtD,QAAI,CAAC1B,mBAAmBvI,QAAQkK,YAAY,GAAG;AAC7C,aAAO;IACT;AAEA,QAAIlK,QAAQkK,iBAAiB/B,eAAe;AAI1C,UAAI6B,OAAOE,iBAAiB9B,gBAAgB;AAC1C,eAAO6B,YAAY;MACrB;AAKA,UAAID,OAAOE,iBAAiBhC,kBAAkB;AAC5C,eACE+B,YAAY,UACXE,kBAAkB,oBACjBT,+BAA+BS,aAAa;MAElD;AAIA,aAAOC,QAAQP,aAAaI,OAAO,CAAC;IACtC;AAEA,QAAIjK,QAAQkK,iBAAiBhC,kBAAkB;AAI7C,UAAI8B,OAAOE,iBAAiB9B,gBAAgB;AAC1C,eAAO6B,YAAY;MACrB;AAIA,UAAID,OAAOE,iBAAiB/B,eAAe;AACzC,eAAO8B,YAAY,UAAUN,wBAAwBQ,aAAa;MACpE;AAIA,aAAOC,QAAQN,gBAAgBG,OAAO,CAAC;IACzC;AAEA,QAAIjK,QAAQkK,iBAAiB9B,gBAAgB;AAI3C,UACE4B,OAAOE,iBAAiB/B,iBACxB,CAACwB,wBAAwBQ,aAAa,GACtC;AACA,eAAO;MACT;AAEA,UACEH,OAAOE,iBAAiBhC,oBACxB,CAACwB,+BAA+BS,aAAa,GAC7C;AACA,eAAO;MACT;AAIA,aACE,CAACL,gBAAgBG,OAAO,MACvBL,6BAA6BK,OAAO,KAAK,CAACJ,aAAaI,OAAO;IAEnE;AAGA,QACExB,sBAAsB,2BACtBF,mBAAmBvI,QAAQkK,YAAY,GACvC;AACA,aAAO;IACT;AAMA,WAAO;;AAQT,MAAMG,eAAe,SAAfA,cAAyBC,MAAM;AACnCzM,cAAUsF,UAAUI,SAAS;MAAEvD,SAASsK;IAAK,CAAC;AAC9C,QAAI;AAEFA,WAAKC,WAAWC,YAAYF,IAAI;aACzBtH,GAAG;AACV,UAAI;AACFsH,aAAKG,YAAY1F;eACV/B,IAAG;AACVsH,aAAKI,OAAM;MACb;IACF;;AASF,MAAMC,mBAAmB,SAAnBA,kBAA6BC,MAAMN,MAAM;AAC7C,QAAI;AACFzM,gBAAUsF,UAAUI,SAAS;QAC3BsH,WAAWP,KAAKQ,iBAAiBF,IAAI;QACrCG,MAAMT;MACR,CAAC;aACMtH,GAAG;AACVnF,gBAAUsF,UAAUI,SAAS;QAC3BsH,WAAW;QACXE,MAAMT;MACR,CAAC;IACH;AAEAA,SAAKU,gBAAgBJ,IAAI;AAGzB,QAAIA,SAAS,QAAQ,CAAC9E,aAAa8E,IAAI,GAAG;AACxC,UAAIzD,cAAcC,qBAAqB;AACrC,YAAI;AACFiD,uBAAaC,IAAI;QACnB,SAAStH,GAAG;QAAA;MACd,OAAO;AACL,YAAI;AACFsH,eAAKW,aAAaL,MAAM,EAAE;QAC5B,SAAS5H,GAAG;QAAA;MACd;IACF;;AASF,MAAMkI,gBAAgB,SAAhBA,eAA0BC,OAAO;AAErC,QAAIC;AACJ,QAAIC;AAEJ,QAAInE,YAAY;AACdiE,cAAQ,sBAAsBA;IAChC,OAAO;AAEL,UAAMG,UAAUlN,YAAY+M,OAAO,aAAa;AAChDE,0BAAoBC,WAAWA,QAAQ,CAAC;IAC1C;AAEA,QACE7C,sBAAsB,2BACtBJ,cAAcD,gBACd;AAEA+C,cACE,mEACAA,QACA;IACJ;AAEA,QAAMI,eAAezG,qBACjBA,mBAAmBjC,WAAWsI,KAAK,IACnCA;AAKJ,QAAI9C,cAAcD,gBAAgB;AAChC,UAAI;AACFgD,cAAM,IAAIhH,UAAS,EAAGoH,gBAAgBD,cAAc9C,iBAAiB;MACvE,SAASzF,GAAG;MAAA;IACd;AAGA,QAAI,CAACoI,OAAO,CAACA,IAAIK,iBAAiB;AAChCL,YAAMnG,eAAeyG,eAAerD,WAAW,YAAY,IAAI;AAC/D,UAAI;AACF+C,YAAIK,gBAAgBE,YAAYrD,iBAC5BvD,YACAwG;eACGvI,GAAG;MACV;IAEJ;AAEA,QAAM4I,OAAOR,IAAIQ,QAAQR,IAAIK;AAE7B,QAAIN,SAASE,mBAAmB;AAC9BO,WAAKC,aACHzJ,SAAS0J,eAAeT,iBAAiB,GACzCO,KAAKG,WAAW,CAAC,KAAK,IACxB;IACF;AAGA,QAAI1D,cAAcD,gBAAgB;AAChC,aAAOhD,qBAAqB4G,KAC1BZ,KACApE,iBAAiB,SAAS,MAC5B,EAAE,CAAC;IACL;AAEA,WAAOA,iBAAiBoE,IAAIK,kBAAkBG;;AAShD,MAAMK,kBAAkB,SAAlBA,iBAA4B7I,MAAM;AACtC,WAAO8B,mBAAmB8G;MACxB5I,KAAKyB,iBAAiBzB;MACtBA;;MAEAW,WAAWmI,eACTnI,WAAWoI,eACXpI,WAAWqI,YACXrI,WAAWsI,8BACXtI,WAAWuI;MACb;MACA;IACF;;AASF,MAAMC,eAAe,SAAfA,cAAyBC,KAAK;AAClC,WACEA,eAAerI,oBACd,OAAOqI,IAAIC,aAAa,YACvB,OAAOD,IAAIE,gBAAgB,YAC3B,OAAOF,IAAIhC,gBAAgB,cAC3B,EAAEgC,IAAIG,sBAAsB1I,iBAC5B,OAAOuI,IAAIxB,oBAAoB,cAC/B,OAAOwB,IAAIvB,iBAAiB,cAC5B,OAAOuB,IAAItC,iBAAiB,YAC5B,OAAOsC,IAAIX,iBAAiB,cAC5B,OAAOW,IAAII,kBAAkB;;AAUnC,MAAMC,UAAU,SAAVA,SAAoB1M,QAAQ;AAChC,WAAOkC,QAAOwB,IAAI,MAAK,WACnB1D,kBAAkB0D,OAClB1D,UACEkC,QAAOlC,MAAM,MAAK,YAClB,OAAOA,OAAOqD,aAAa,YAC3B,OAAOrD,OAAOsM,aAAa;;AAWnC,MAAMK,eAAe,SAAfA,cAAyBC,YAAYC,aAAaC,MAAM;AAC5D,QAAI,CAAC1H,MAAMwH,UAAU,GAAG;AACtB;IACF;AAEAzP,iBAAaiI,MAAMwH,UAAU,GAAG,SAACG,MAAS;AACxCA,WAAKlB,KAAK7I,WAAW6J,aAAaC,MAAMrE,MAAM;IAChD,CAAC;;AAaH,MAAMuE,oBAAoB,SAApBA,mBAA8BH,aAAa;AAC/C,QAAIpI;AAGJkI,iBAAa,0BAA0BE,aAAa,IAAI;AAGxD,QAAIT,aAAaS,WAAW,GAAG;AAC7B3C,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QAAIpO,WAAW,mBAAmBoO,YAAYP,QAAQ,GAAG;AACvDpC,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QAAM/C,UAAUpK,kBAAkBmN,YAAYP,QAAQ;AAGtDK,iBAAa,uBAAuBE,aAAa;MAC/C/C;MACAmD,aAAa1H;IACf,CAAC;AAGD,QACEsH,YAAYJ,cAAa,KACzB,CAACC,QAAQG,YAAYK,iBAAiB,MACrC,CAACR,QAAQG,YAAYpI,OAAO,KAC3B,CAACiI,QAAQG,YAAYpI,QAAQyI,iBAAiB,MAChDzO,WAAW,WAAWoO,YAAYrB,SAAS,KAC3C/M,WAAW,WAAWoO,YAAYN,WAAW,GAC7C;AACArC,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QACE/C,YAAY,YACZrL,WAAW,cAAcoO,YAAYrB,SAAS,GAC9C;AACAtB,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QAAIA,YAAYxJ,aAAa,GAAG;AAC9B6G,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QACEjG,gBACAiG,YAAYxJ,aAAa,KACzB5E,WAAW,WAAWoO,YAAYC,IAAI,GACtC;AACA5C,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QAAI,CAACtH,aAAauE,OAAO,KAAKzD,YAAYyD,OAAO,GAAG;AAElD,UAAI,CAACzD,YAAYyD,OAAO,KAAKqD,wBAAwBrD,OAAO,GAAG;AAC7D,YACEhE,wBAAwBC,wBAAwBrH,UAChDD,WAAWqH,wBAAwBC,cAAc+D,OAAO;AAExD,iBAAO;AACT,YACEhE,wBAAwBC,wBAAwB8C,YAChD/C,wBAAwBC,aAAa+D,OAAO;AAE5C,iBAAO;MACX;AAGA,UAAIxC,gBAAgB,CAACG,gBAAgBqC,OAAO,GAAG;AAC7C,YAAMM,aAAa9F,cAAcuI,WAAW,KAAKA,YAAYzC;AAC7D,YAAMwB,aAAavH,cAAcwI,WAAW,KAAKA,YAAYjB;AAE7D,YAAIA,cAAcxB,YAAY;AAC5B,cAAMgD,aAAaxB,WAAWzM;AAE9B,mBAASkO,IAAID,aAAa,GAAGC,KAAK,GAAG,EAAEA,GAAG;AACxC,gBAAMC,aAAanJ,UAAUyH,WAAWyB,CAAC,GAAG,IAAI;AAChDC,uBAAWC,kBAAkBV,YAAYU,kBAAkB,KAAK;AAChEnD,uBAAWsB,aAAa4B,YAAYlJ,eAAeyI,WAAW,CAAC;UACjE;QACF;MACF;AAEA3C,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QAAIA,uBAAuBlJ,WAAW,CAACiG,qBAAqBiD,WAAW,GAAG;AACxE3C,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,SACG/C,YAAY,cACXA,YAAY,aACZA,YAAY,eACdrL,WAAW,+BAA+BoO,YAAYrB,SAAS,GAC/D;AACAtB,mBAAa2C,WAAW;AACxB,aAAO;IACT;AAGA,QAAIlG,sBAAsBkG,YAAYxJ,aAAa,GAAG;AAEpDoB,gBAAUoI,YAAYN;AACtB9H,gBAAUtG,cAAcsG,SAAStD,iBAAe,GAAG;AACnDsD,gBAAUtG,cAAcsG,SAASrD,YAAU,GAAG;AAC9CqD,gBAAUtG,cAAcsG,SAASpD,eAAa,GAAG;AACjD,UAAIwL,YAAYN,gBAAgB9H,SAAS;AACvC/G,kBAAUsF,UAAUI,SAAS;UAAEvD,SAASgN,YAAY1I,UAAS;QAAG,CAAC;AACjE0I,oBAAYN,cAAc9H;MAC5B;IACF;AAGAkI,iBAAa,yBAAyBE,aAAa,IAAI;AAEvD,WAAO;;AAYT,MAAMW,oBAAoB,SAApBA,mBAA8BC,OAAOC,QAAQnN,OAAO;AAExD,QACE4G,iBACCuG,WAAW,QAAQA,WAAW,YAC9BnN,SAAS0B,YAAY1B,SAASmI,cAC/B;AACA,aAAO;IACT;AAMA,QACElC,mBACA,CAACF,YAAYoH,MAAM,KACnBjP,WAAW6C,aAAWoM,MAAM;AAC5B;aAESnH,mBAAmB9H,WAAW8C,aAAWmM,MAAM;AAAG;aAGlD,CAAC/H,aAAa+H,MAAM,KAAKpH,YAAYoH,MAAM,GAAG;AACvD;;;;QAIGP,wBAAwBM,KAAK,MAC1B3H,wBAAwBC,wBAAwBrH,UAChDD,WAAWqH,wBAAwBC,cAAc0H,KAAK,KACrD3H,wBAAwBC,wBAAwB8C,YAC/C/C,wBAAwBC,aAAa0H,KAAK,OAC5C3H,wBAAwBK,8BAA8BzH,UACtDD,WAAWqH,wBAAwBK,oBAAoBuH,MAAM,KAC5D5H,wBAAwBK,8BAA8B0C,YACrD/C,wBAAwBK,mBAAmBuH,MAAM;;QAGtDA,WAAW,QACV5H,wBAAwBM,mCACtBN,wBAAwBC,wBAAwBrH,UAChDD,WAAWqH,wBAAwBC,cAAcxF,KAAK,KACrDuF,wBAAwBC,wBAAwB8C,YAC/C/C,wBAAwBC,aAAaxF,KAAK;;AAChD;WAGK;AACL,eAAO;MACT;IAEF,WAAWsH,oBAAoB6F,MAAM;AAAG;aAKtCjP,WAAW+C,kBAAgBrD,cAAcoC,OAAOmB,mBAAiB,EAAE,CAAC;AACpE;cAKCgM,WAAW,SAASA,WAAW,gBAAgBA,WAAW,WAC3DD,UAAU,YACVpP,cAAckC,OAAO,OAAO,MAAM,KAClCoH,cAAc8F,KAAK;AACnB;aAMAhH,2BACA,CAAChI,WAAWgD,qBAAmBtD,cAAcoC,OAAOmB,mBAAiB,EAAE,CAAC;AACxE;aAGSnB,OAAO;AAChB,aAAO;IACT;AAAO;AAKP,WAAO;;AAST,MAAM4M,0BAA0B,SAA1BA,yBAAoCrD,SAAS;AACjD,WAAOA,YAAY,oBAAoB7L,YAAY6L,SAASlI,gBAAc;;AAa5E,MAAM+L,sBAAsB,SAAtBA,qBAAgCd,aAAa;AACjD,QAAIe;AACJ,QAAIrN;AACJ,QAAImN;AACJ,QAAI9N;AAEJ+M,iBAAa,4BAA4BE,aAAa,IAAI;AAE1D,QAAQL,aAAeK,YAAfL;AAGR,QAAI,CAACA,YAAY;AACf;IACF;AAEA,QAAMqB,YAAY;MAChBC,UAAU;MACVC,WAAW;MACXC,UAAU;MACVC,mBAAmBtI;;AAErB/F,QAAI4M,WAAWrN;AAGf,WAAOS,KAAK;AACVgO,aAAOpB,WAAW5M,CAAC;AACnB,UAAAsO,QAA+BN,MAAvBnD,OAAIyD,MAAJzD,MAAMV,eAAYmE,MAAZnE;AACdxJ,cAAQkK,SAAS,UAAUmD,KAAKrN,QAAQhC,WAAWqP,KAAKrN,KAAK;AAC7DmN,eAAShO,kBAAkB+K,IAAI;AAG/BoD,gBAAUC,WAAWJ;AACrBG,gBAAUE,YAAYxN;AACtBsN,gBAAUG,WAAW;AACrBH,gBAAUM,gBAAgBpL;AAC1B4J,mBAAa,yBAAyBE,aAAagB,SAAS;AAC5DtN,cAAQsN,UAAUE;AAGlB,UAAIF,UAAUM,eAAe;AAC3B;MACF;AAGA3D,uBAAiBC,MAAMoC,WAAW;AAGlC,UAAI,CAACgB,UAAUG,UAAU;AACvB;MACF;AAGA,UAAI,CAACtH,4BAA4BjI,WAAW,QAAQ8B,KAAK,GAAG;AAC1DiK,yBAAiBC,MAAMoC,WAAW;AAClC;MACF;AAGA,UAAIlG,oBAAoB;AACtBpG,gBAAQpC,cAAcoC,OAAOY,iBAAe,GAAG;AAC/CZ,gBAAQpC,cAAcoC,OAAOa,YAAU,GAAG;AAC1Cb,gBAAQpC,cAAcoC,OAAOc,eAAa,GAAG;MAC/C;AAGA,UAAMoM,QAAQ/N,kBAAkBmN,YAAYP,QAAQ;AACpD,UAAI,CAACkB,kBAAkBC,OAAOC,QAAQnN,KAAK,GAAG;AAC5C;MACF;AAKA,UAAI6G,yBAAyBsG,WAAW,QAAQA,WAAW,SAAS;AAElElD,yBAAiBC,MAAMoC,WAAW;AAGlCtM,gBAAQ8G,8BAA8B9G;MACxC;AAGA,UAAIqG,gBAAgBnI,WAAW,iCAAiC8B,KAAK,GAAG;AACtEiK,yBAAiBC,MAAMoC,WAAW;AAClC;MACF;AAGA,UACElI,sBACAzC,QAAOF,YAAY,MAAK,YACxB,OAAOA,aAAaoM,qBAAqB,YACzC;AACA,YAAIrE;AAAc;aAEX;AACL,kBAAQ/H,aAAaoM,iBAAiBX,OAAOC,MAAM,GAAC;YAClD,KAAK,eAAe;AAClBnN,sBAAQoE,mBAAmBjC,WAAWnC,KAAK;AAC3C;YACF;YAEA,KAAK,oBAAoB;AACvBA,sBAAQoE,mBAAmBhC,gBAAgBpC,KAAK;AAChD;YACF;UAKF;QACF;MACF;AAGA,UAAI;AACF,YAAIwJ,cAAc;AAChB8C,sBAAYwB,eAAetE,cAAcU,MAAMlK,KAAK;QACtD,OAAO;AAELsM,sBAAY/B,aAAaL,MAAMlK,KAAK;QACtC;AAEA,YAAI6L,aAAaS,WAAW,GAAG;AAC7B3C,uBAAa2C,WAAW;QAC1B,OAAO;AACLrP,mBAASwF,UAAUI,OAAO;QAC5B;MACF,SAASP,GAAG;MAAA;IACd;AAGA8J,iBAAa,2BAA2BE,aAAa,IAAI;;AAQ3D,MAAMyB,qBAAqB,SAArBA,oBAA+BC,UAAU;AAC7C,QAAIC;AACJ,QAAMC,iBAAiB3C,gBAAgByC,QAAQ;AAG/C5B,iBAAa,2BAA2B4B,UAAU,IAAI;AAEtD,WAAQC,aAAaC,eAAeC,SAAQ,GAAK;AAE/C/B,mBAAa,0BAA0B6B,YAAY,IAAI;AAGvD,UAAIxB,kBAAkBwB,UAAU,GAAG;AACjC;MACF;AAGA,UAAIA,WAAW/J,mBAAmBjB,kBAAkB;AAClD8K,QAAAA,oBAAmBE,WAAW/J,OAAO;MACvC;AAGAkJ,0BAAoBa,UAAU;IAChC;AAGA7B,iBAAa,0BAA0B4B,UAAU,IAAI;;AAWvDvL,YAAU2L,WAAW,SAAU3D,OAAiB;AAAA,QAAVjC,MAAG7J,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAA6D,SAAA7D,UAAA,CAAA,IAAG,CAAA;AAC1C,QAAIuM;AACJ,QAAImD;AACJ,QAAI/B;AACJ,QAAIgC;AACJ,QAAIC;AAIJ3G,qBAAiB,CAAC6C;AAClB,QAAI7C,gBAAgB;AAClB6C,cAAQ;IACV;AAGA,QAAI,OAAOA,UAAU,YAAY,CAAC0B,QAAQ1B,KAAK,GAAG;AAChD,UAAI,OAAOA,MAAMhN,aAAa,YAAY;AACxCgN,gBAAQA,MAAMhN,SAAQ;AACtB,YAAI,OAAOgN,UAAU,UAAU;AAC7B,gBAAMpM,gBAAgB,iCAAiC;QACzD;MACF,OAAO;AACL,cAAMA,gBAAgB,4BAA4B;MACpD;IACF;AAGA,QAAI,CAACoE,UAAUM,aAAa;AAC1B,UACEpB,QAAOJ,QAAOiN,YAAY,MAAK,YAC/B,OAAOjN,QAAOiN,iBAAiB,YAC/B;AACA,YAAI,OAAO/D,UAAU,UAAU;AAC7B,iBAAOlJ,QAAOiN,aAAa/D,KAAK;QAClC;AAEA,YAAI0B,QAAQ1B,KAAK,GAAG;AAClB,iBAAOlJ,QAAOiN,aAAa/D,MAAMV,SAAS;QAC5C;MACF;AAEA,aAAOU;IACT;AAGA,QAAI,CAAClE,YAAY;AACfgC,mBAAaC,GAAG;IAClB;AAGA/F,cAAUI,UAAU,CAAA;AAGpB,QAAI,OAAO4H,UAAU,UAAU;AAC7BzD,iBAAW;IACb;AAEA,QAAIA,UAAU;AAEZ,UAAIyD,MAAMsB,UAAU;AAClB,YAAMxC,UAAUpK,kBAAkBsL,MAAMsB,QAAQ;AAChD,YAAI,CAAC/G,aAAauE,OAAO,KAAKzD,YAAYyD,OAAO,GAAG;AAClD,gBAAMlL,gBACJ,yDACF;QACF;MACF;IACF,WAAWoM,iBAAiBtH,MAAM;AAGhC+H,aAAOV,cAAc,SAAS;AAC9B6D,qBAAenD,KAAK/G,cAAcQ,WAAW8F,OAAO,IAAI;AACxD,UAAI4D,aAAavL,aAAa,KAAKuL,aAAatC,aAAa,QAAQ;AAEnEb,eAAOmD;MACT,WAAWA,aAAatC,aAAa,QAAQ;AAC3Cb,eAAOmD;MACT,OAAO;AAELnD,aAAKuD,YAAYJ,YAAY;MAC/B;IACF,OAAO;AAEL,UACE,CAAC5H,cACD,CAACL,sBACD,CAACE;MAEDmE,MAAM1M,QAAQ,GAAG,MAAM,IACvB;AACA,eAAOqG,sBAAsBuC,sBACzBvC,mBAAmBjC,WAAWsI,KAAK,IACnCA;MACN;AAGAS,aAAOV,cAAcC,KAAK;AAG1B,UAAI,CAACS,MAAM;AACT,eAAOzE,aAAa,OAAOE,sBAAsBtC,YAAY;MAC/D;IACF;AAGA,QAAI6G,QAAQ1E,YAAY;AACtBmD,mBAAauB,KAAKwD,UAAU;IAC9B;AAGA,QAAMC,eAAepD,gBAAgBvE,WAAWyD,QAAQS,IAAI;AAG5D,WAAQoB,cAAcqC,aAAaR,SAAQ,GAAK;AAE9C,UAAI7B,YAAYxJ,aAAa,KAAKwJ,gBAAgBgC,SAAS;AACzD;MACF;AAGA,UAAI7B,kBAAkBH,WAAW,GAAG;AAClC;MACF;AAGA,UAAIA,YAAYpI,mBAAmBjB,kBAAkB;AACnD8K,2BAAmBzB,YAAYpI,OAAO;MACxC;AAGAkJ,0BAAoBd,WAAW;AAE/BgC,gBAAUhC;IACZ;AAEAgC,cAAU;AAGV,QAAItH,UAAU;AACZ,aAAOyD;IACT;AAGA,QAAIhE,YAAY;AACd,UAAIC,qBAAqB;AACvB6H,qBAAa9J,uBAAuB6G,KAAKJ,KAAK/G,aAAa;AAE3D,eAAO+G,KAAKwD,YAAY;AAEtBH,qBAAWE,YAAYvD,KAAKwD,UAAU;QACxC;MACF,OAAO;AACLH,qBAAarD;MACf;AAEA,UAAI9F,aAAawJ,cAAcxJ,aAAayJ,eAAe;AAQzDN,qBAAa5J,WAAW2G,KAAKtI,kBAAkBuL,YAAY,IAAI;MACjE;AAEA,aAAOA;IACT;AAEA,QAAIO,iBAAiBxI,iBAAiB4E,KAAKnB,YAAYmB,KAAKD;AAG5D,QACE3E,kBACAtB,aAAa,UAAU,KACvBkG,KAAK/G,iBACL+G,KAAK/G,cAAc4K,WACnB7D,KAAK/G,cAAc4K,QAAQ7E,QAC3BhM,WAAW6G,cAA0BmG,KAAK/G,cAAc4K,QAAQ7E,IAAI,GACpE;AACA4E,uBACE,eAAe5D,KAAK/G,cAAc4K,QAAQ7E,OAAO,QAAQ4E;IAC7D;AAGA,QAAI1I,oBAAoB;AACtB0I,uBAAiBlR,cAAckR,gBAAgBlO,iBAAe,GAAG;AACjEkO,uBAAiBlR,cAAckR,gBAAgBjO,YAAU,GAAG;AAC5DiO,uBAAiBlR,cAAckR,gBAAgBhO,eAAa,GAAG;IACjE;AAEA,WAAOsD,sBAAsBuC,sBACzBvC,mBAAmBjC,WAAW2M,cAAc,IAC5CA;;AASNrM,YAAUuM,YAAY,SAAUxG,KAAK;AACnCD,iBAAaC,GAAG;AAChBjC,iBAAa;;AAQf9D,YAAUwM,cAAc,WAAY;AAClC/G,aAAS;AACT3B,iBAAa;;AAaf9D,YAAUyM,mBAAmB,SAAUC,KAAK9B,MAAMrN,OAAO;AAEvD,QAAI,CAACkI,QAAQ;AACXK,mBAAa,CAAA,CAAE;IACjB;AAEA,QAAM2E,QAAQ/N,kBAAkBgQ,GAAG;AACnC,QAAMhC,SAAShO,kBAAkBkO,IAAI;AACrC,WAAOJ,kBAAkBC,OAAOC,QAAQnN,KAAK;;AAU/CyC,YAAU2M,UAAU,SAAU/C,YAAYgD,cAAc;AACtD,QAAI,OAAOA,iBAAiB,YAAY;AACtC;IACF;AAEAxK,UAAMwH,UAAU,IAAIxH,MAAMwH,UAAU,KAAK,CAAA;AACzClP,cAAU0H,MAAMwH,UAAU,GAAGgD,YAAY;;AAW3C5M,YAAU6M,aAAa,SAAUjD,YAAY;AAC3C,QAAIxH,MAAMwH,UAAU,GAAG;AACrB,aAAOpP,SAAS4H,MAAMwH,UAAU,CAAC;IACnC;;AASF5J,YAAU8M,cAAc,SAAUlD,YAAY;AAC5C,QAAIxH,MAAMwH,UAAU,GAAG;AACrBxH,YAAMwH,UAAU,IAAI,CAAA;IACtB;;AAQF5J,YAAU+M,iBAAiB,WAAY;AACrC3K,YAAQ,CAAA;;AAGV,SAAOpC;AACT;AAEA,IAAA,SAAeF,gBAAe;", "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "_ref", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "_construct", "_toConsumableArray", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "get", "value", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "_typeof", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "concat", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_basicCustomElementTest", "childCount", "i", "child<PERSON>lone", "__removalCount", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "_attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmod", "serializedHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"]}