<template>
  <div class="nav" :class="{ 'hide-title_nav': props?.config?.hideTitle }">
    <div class="left" @click="router.push({name:'home'})" v-if="!props?.config?.hideTitle">
      <!-- <img class="logo" src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg" alt="logo"> -->
      <DropboxOutlined class="logo"  />
      <div>{{ baseConfig.title }}</div>
    </div>
    <div v-else></div>
    <div class="right">
      <a-avatar class="face_wrap">
        <template #icon>
          <UserOutlined />
        </template>
      </a-avatar>
      <a-dropdown class="drop_wrap" >
        <a class="ant-dropdown-link" @click.prevent>
          {{ userStore.userInfo.username }}
          <DownOutlined />
        </a>
        <template #overlay>
          <a-menu>
            <!-- <a-menu-item>
              <UserOutlined style="margin-right: 10px;"/>
              <a href="javascript:;" @click="router.push({name:'personal'})">个人信息</a>
            </a-menu-item> -->
            <a-menu-item>
              <LoginOutlined  style="margin-right: 10px;"/>
              <a href="javascript:;" @click="toLoginOut">退出登录</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-dropdown class="drop_wrap" v-if="false">
        <a class="ant-dropdown-link" @click.prevent>
          <GlobalOutlined style="margin-right: 5px;" />
          {{ getCurrentLanguageText() }}
          <DownOutlined />
        </a>
        <template #overlay>
          <a-menu :selectedKeys="[baseStore.lang]">
            <a-menu-item key="zh_CN">
              <a href="javascript:;" @click="setLang('zh_CN')">{{ $t('common.language.chinese') }}</a>
            </a-menu-item>
            <a-menu-item key="en_US">
              <a href="javascript:;" @click="setLang('en_US')">{{ $t('common.language.english') }}</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <a-switch
        v-if="false"
        checked-children="夜间" un-checked-children="白日"
        v-model:checked="checkedTheme" @change="changeTheme"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { useUserInfo } from '@/stores/auth'
import { DownOutlined, UserOutlined, LoginOutlined, GlobalOutlined, DropboxOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { loginOut } from '@/api/auth'
import { useBaseStore } from '@/stores/base'
import { useI18n } from 'vue-i18n'
import { baseConfig } from '@/config/baseConfig'

const props = defineProps<{
  config?: {
    hideTitle?: boolean;
  }
}>()
const { t } = useI18n()
const router = useRouter()
const userStore = useUserInfo()
const baseStore = useBaseStore()

const checkedTheme = ref(false)

// 获取当前语言显示文本
const getCurrentLanguageText = () => {
  return baseStore.lang === 'zh_CN' ? t('common.language.chinese') : t('common.language.english')
}

const toLoginOut = async () => {
  const { code } = await loginOut()
  if (code === 0) {
    router.push({ name: 'login' })
  }
}

const setLang = (lang: string) => {
  baseStore.setLang(lang)
  // // console.log('lang:', lang)
  // locale.value = lang
}

const changeTheme = () => {
  if (checkedTheme.value) {
    document.documentElement.setAttribute('theme', 'dark')
  } else {
    document.documentElement.removeAttribute('theme')
  }
}

onMounted(async () => {
  await userStore.fetchUserInfo()
  // console.log('head', userStore.userInfo)
  // // console.log('userStore:', userStore, userStore.userInfo)
})

</script>

<style scoped lang="less">
@import '@/style/base.less';

  .nav {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background: rgba(54, 64, 84, 1);
    align-items: center;
    .left {
      display: flex;
      color: #fff;
      font-size: 20px;
      &:hover {
        cursor: pointer;
      }
      .logo {
        width: 28px;
        margin-right: 9px;
        color: rgba(44, 170, 137, 1);
      }
    }
    .right {
      display: flex;
      align-items: center;
      .face_wrap {
        margin-right: 12px;
        // background: #1677ff;
      }
      .drop_wrap {
        margin-right: 24px;
        color: #fff;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
  .hide-title_nav{
    background: #fff;
    .right {
      .face_wrap {
        background: @baseColor;

      }
      .drop_wrap {
        color: #333;
      }
    }
  }
</style>