import dayjs from 'dayjs'
export const downloadFile = async (url: string, name: string) => {
  const a = document.createElement("a");
  a.href = url; 
  a.download = name;
  a.style.display = "none";
  document.body.appendChild(a);
  a.click();
  a.remove();
}

export const formatDate = (timestamp: number) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm') 
}

export const strToBase64 = async (str: string) => {
  const encoder = new TextEncoder();
  const data = encoder.encode(str);
  let binary = '';
  for (let i = 0; i < data.length; i++) {
    binary += String.fromCharCode(data[i]);
  }
  return btoa(binary);
}

export const getOptionByValue = (options: { label: string, value: any }[], value: any) => {
  return options ? options.find(option => option.value === value ) : value;
}

const addZero = (str: string, length: number) => {
  const len = str.length
  if (len < length) {
    for (let i = 0; i < length -  len; i++) {
      str = '0' + str
    }
  }
  return str
}

export const saveNunberPoint = (num: number, length: number = 2) => {
  if (num === undefined || num === null) {
    return num
  }
  const len = Math.pow(10, length)
  return Math.round(len * num) / len
}

export const getXDataFromZero = (num: number) => {
  const result: number[] = []
  for (let i = 0; i < num; i++) {
    result.push(i)
  }
  return result
}

