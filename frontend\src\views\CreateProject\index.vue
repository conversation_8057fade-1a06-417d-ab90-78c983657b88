<template>
  <div class="body_wrap">
    <div class="part_wrap">
      <div>
        <a-breadcrumb>
          <a-breadcrumb-item><a href="void:0" @click="router.back()"> < 返回</a></a-breadcrumb-item>
          <a-breadcrumb-item>{{ viceTitle }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
        <div class="content_wrap">
          <a-spin :spinning="state.loading">
            <a-form
              ref="formRef"
              labelAlign="left"
              :model="formState"
              name="basic"
              :label-col="{ span: 9 }"
              :wrapper-col="{ span: 12 }"
              autocomplete="off">
              <div class="box_wrap">
                <div class="b_title">项目信息</div>
                <div class="b_body">
                  <div class="line_item">
                    <a-form-item
                      v-for="item in formBasicInfo()"
                      class="line_form_item"
                      :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                      :name="item.name"
                      :rules="item.rules"
                    >
                      <template v-if="route.query.projectId && ['projectName', 'customer', 'desc'].includes(item.name)">
                        <div>{{ formState[item.name] }}</div>
                      </template>
                      <template v-else>
                        <!-- <a-input-number
                          class="input_deal_wrap" :defaultValue="item.default"
                          v-model:value="formState[item.name]" size="small"
                          v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                          :formatter="value => `${(value * 100).toFixed(2)}`"
                          :parser="value => parseFloat(value) / 100"
                          :min="0"
                          :max="1"
                          :step="0.01"
                        /> -->
                        <a-input-number
                          class="input_deal_wrap" :defaultValue="item.default"
                          v-model:value="formState[item.name]" size="small"
                          v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                          :min="0"
                          :max="1"
                          :step="0.01"
                        />
                        <a-input-number
                          style="width:80%"
                          class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]" size="small"
                          v-if="item.type === 'number'&&item.numberType !== 'ratio'"  :min="0"
                        />
                        <a-input
                          style="width:80%"
                          class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]" size="small"
                          v-else-if="item.type==='string'"
                          />
                        <a-textarea
                          style="width:80%"
                          class="input_deal_wrap"
                          :style="{margin: '5px 0 5px 0'}"
                          :autosize="{ maxRows: 3 }"
                          :defaultValue="item.default" v-model:value="formState[item.name]" size="small" v-else-if="item.type==='textarea'"
                        />
                      </template>
                    </a-form-item>
                  </div>
                </div>
              </div>
              <div class="box_wrap">
                <div class="b_title">求解方式</div>
                <div class="b_body">
                  <div class="goal_list">
                    <div class="g_title">求解目标选择</div>
                    <div
                      v-for="(item, index) in formGoalList()"
                      @click="selGoal(index)"
                      :name="item.name"
                      :rules="item.rules"
                      :class="{sel_item: selGoalVal[index] }"
                    >
                      <div class="goal_item">
                        <div class="name">{{ item.label }}</div>
                        <div class="v_line" v-show="selGoalVal[index]">|</div>
                        <div class="number_input" v-show="selGoalVal[index]">
                          <a-input-number
                            @change="selGoal(index, true)"
                            :controls="false"
                            @click="e=>{e.stopPropagation();}"
                            @step.prevent.stop="e=>e.stopPropagation()"
                            class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]" size="small" 
                            :formatter="value => `${value*100}%`"
                            :parser="value => parseFloat(value.replace('%', '')) / 100"
                            :min="0"
                            :max="1"
                          />
                          <!-- <a-input-number
                              class="input_deal_wrap" :defaultValue="item.default"
                              v-model:value="formState[item.name]" size="small"
                              v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                              :formatter="value => `${value*100}%`"
                              :parser="value => parseFloat(value.replace('%', '')) / 100"
                              :min="0"
                              :max="1"
                              :step="0.01"
                            />  -->
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="g_tips" >选择多个求解目标后，需设置每个目标的权重，总权重值需为100%</div>
                  <div class="goal_list">
                    <div class="g_title">求解算法</div>
                    <div>
                      <a-radio-group
                        size="small"
                        v-model:value="formState.algorithm" button-style="solid">
                        <a-radio-button v-for="item in formAlgList()" :value="item.default">{{ item.label }}</a-radio-button>
                      </a-radio-group>
                    </div>
                  </div>

                </div>
              </div>
              <div class="box_wrap">
                <div class="b_title">场景选择</div>
                <div class="b_body">
                  <div class="scene_title_wrap">
                    <div class="pv_wind_wrap" >
                      <div class="s_t_item_sp" @click="selScene(0)" :class="{s_t_item_sel: selSceneVal[0]}">
                        <div class="left" >
                          <img src="@/assets/imgs/createProject/pv.svg" />
                        </div>
                        <div class="right" >
                          <div class="r_title" >光伏</div>
                          <div class="r_desc" >选择光伏场景后可配置光伏参数</div>
                        </div>
                      </div>
                      <div class="s_t_item_sp" @click="selScene(1)" :class="{s_t_item_sel: selSceneVal[1]}">
                        <div class="left" >
                          <img src="@/assets/imgs/createProject/wind.svg" />
                        </div>
                        <div class="right" >
                          <div class="r_title" >风机</div>
                          <div class="r_desc" >选择风机场景后可配置风机参数</div>
                        </div>
                      </div>
                    </div>
                    <div class="s_t_item" @click="selScene(2)" :class="{s_t_item_sel: selSceneVal[2]}">
                      <div class="left" >
                        <img src="@/assets/imgs/createProject/grid.svg" />
                      </div>
                      <div class="right" >
                        <div class="r_title" >电网</div>
                        <div class="r_desc" >选择电网场景后可配置电网用电</div>
                      </div>
                    </div>
                    <div class="s_t_item" @click="selScene(3)" :class="{s_t_item_sel: selSceneVal[3]}">
                      <div class="left" >
                        <img src="@/assets/imgs/createProject/chuneng.svg" />
                      </div>
                      <div class="right" >
                        <div class="r_title" >储能</div>
                        <div class="r_desc" >选择储能场景后可配置储能参数</div>
                      </div>
                    </div>
                    <div class="s_t_item" @click="selScene(4)" :class="{s_t_item_sel: selSceneVal[4]}">
                      <div class="left" >
                        <img src="@/assets/imgs/createProject/alk.svg" />
                      </div>
                      <div class="right" >
                        <div class="r_title" >制氢</div>
                        <div class="r_desc" >选择制氢场景后可配置制氢参数</div>
                      </div>
                    </div>
                    <div class="s_t_item" @click="selScene(5)" :class="{s_t_item_sel: selSceneVal[5]}">
                      <div class="left" >
                        <img src="@/assets/imgs/createProject/chuqing.svg" />
                      </div>
                      <div class="right" >
                        <div class="r_title" >储氢</div>
                        <div class="r_desc" >选择储氢场景后可配置储氢参数</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="box_wrap">
                <div class="b_title">设备配置</div>
                <div class="b_body">
                  
                  <div class="scene_content_wrap">
                    <a-tabs v-model:activeKey="activeKey">
                      <a-tab-pane
                        v-for="item in curTabKeys"
                        :key="item.key"
                        :tab="item.title">
                        <!-- 光伏 -->
                        <div v-show="item.key === 0" >
                          <!-- <div class="k_v_list" style="margin-bottom: 15px;">
                            <div class="k_v_item" >
                              <div class="label">出力曲线:</div>
                                <div class="value">
                                  <a-button type="primary" size="small" @click="PVPowerModalRef.open()">请选择</a-button>
                                  <div class="desc_wrap">{{ pvPowerItem.desc }}</div>
                                </div>
                            </div>
                            <div class="k_v_item" >
                              <div class="label">光伏年衰减率:</div>
                              <div class="value">
                                <a-button type="primary" size="small" @click="PVDeviceModalRef.open()">请选择</a-button>
                                <div class="desc_wrap">{{ pvDeviceItem.desc }}</div>
                              </div>
                            </div>
                       
                          </div> -->
                          <div class="line_item">
                            <a-form-item
                              class="line_form_item"
                              label="出力曲线"
                            >
                              <div class="value">
                                <a-button type="primary" size="small" @click="PVPowerModalRef.open()">{{pvPowerItem.desc?'重新选择':'请选择'  }}</a-button>
                                <div class="desc_wrap">{{ pvPowerItem.desc }}</div>
                              </div>
                            </a-form-item>
                            <a-form-item
                              class="line_form_item"
                              label="年衰减率"
                            >
                              <div class="value">
                                <a-button type="primary" size="small" @click="PVDeviceModalRef.open()">{{ pvDeviceItem?.desc ?'重新选择':'请选择' }}</a-button>
                                <div class="desc_wrap">{{ pvDeviceItem?.desc }}</div>
                              </div>
                            </a-form-item>
                            <a-form-item
                              class="line_form_item"
                              label="容量范围(MW)"
                            >
                              <div class="value range_item">
                                <a-input-number class="input_deal_wrap2" :controls="true" :defaultValue="0" v-model:value="formState.pv_min_capacity" size="small" :min="0" />
                                <div class="middle_line">—</div>
                                <a-input-number class="input_deal_wrap2" :controls="true" v-model:value="formState.pv_max_capacity" size="small" :min="0" />
                              </div>
                            </a-form-item>
                            <a-form-item
                              v-for="item in formPV()"
                              class="line_form_item"
                              :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                              :name="item.name"
                              :rules="item.rules"
                            >
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default"
                                v-model:value="formState[item.name]" size="small"
                                v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                                :formatter="value => `${Decimal(value).mul(Decimal(100))}`"
                                :parser="value => Decimal(value).div(Decimal(100)) "
                                :min="0"
                                :max="1"
                                :step="0.01"
                              />
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]"
                                size="small"
                                v-if="item.type === 'number'&&item.numberType !== 'ratio'" 
                                :min="0"
                              />
                              <a-select
                                v-else-if="item.type === 'select'"
                                ref="select"
                                v-model:value="formState[item.name]"
                                style="width: 120px"
                                @change="handleChange"
                              >
                                <a-select-option
                                  v-for="i in item.options"
                                  :value="i.value">{{ i.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                          </div>
                        </div>
                        <!-- 风机 -->
                        <div v-show="item.key === 1">
                          <!-- <div class="k_v_item" >
                            <div class="label">出力曲线:</div>
                            <div class="value">
                              <a-button type="primary" size="small" @click="WindPowerModalRef.open()">请选择</a-button>
                              <div class="desc_wrap">{{ windPowerItem.desc }}</div>
                            </div>
                          </div> -->
                          <!-- <div class="k_v_item" >
                            <div class="label">风机年衰减率</div>
                            <div class="value">
                              <a-button type="primary" size="small" @click="WindDeviceModalRef.open()">请选择</a-button>
                              <div class="desc_wrap">{{ windDeviceItem.desc }}</div>
                            </div>
                          </div> -->
                          <!-- <div class="k_v_item" >
                            <div class="label">容量范围(MW):</div>
                            <div class="value range_item">
                              <a-input-number class="input_deal_wrap2" :controls="false" v-model:value="formState.wind_min_capacity" size="small" :min="0" />
                              <div class="middle_line">—</div>
                              <a-input-number class="input_deal_wrap2" :controls="false" v-model:value="formState.wind_max_capacity" size="small" :min="0" />
                            </div>
                          </div> -->
                          <div class="line_item">
                            <a-form-item
                              class="line_form_item"
                              label="出力曲线"
                            >
                              <div class="value">
                                <a-button type="primary" size="small" @click="WindPowerModalRef.open()">{{windPowerItem.desc?'重新选择':'请选择'}}</a-button>
                                <div class="desc_wrap">{{ windPowerItem.desc }}</div>
                              </div>
                            </a-form-item>
                            <a-form-item
                              class="line_form_item"
                              label="容量范围(MW)"
                            >
                              <div class="value range_item">
                                <a-input-number class="input_deal_wrap2" :controls="false" v-model:value="formState.wind_min_capacity" size="small" :min="0" />
                                <div class="middle_line">—</div>
                                <a-input-number class="input_deal_wrap2" :controls="false" v-model:value="formState.wind_max_capacity" size="small" :min="0" />
                              </div>
                            </a-form-item>
                            <a-form-item
                              v-for="item in formWind()"
                              class="line_form_item"
                              :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                              :name="item.name"
                              :rules="item.rules"
                            >

                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default"
                                v-model:value="formState[item.name]" size="small"
                                v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                                :formatter="value => `${Decimal(value).mul(Decimal(100))}`"
                                :parser="value => Decimal(value).div(Decimal(100)) "
                                :min="0"
                                :max="1"
                                :step="0.01"
                              />
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]"
                                size="small"
                                v-if="item.type === 'number'&&item.numberType !== 'ratio'" 
                                :min="0"
                              />
                              <a-select
                                v-else-if="item.type === 'select'"
                                ref="select"
                                v-model:value="formState[item.name]"
                                style="width: 120px"
                                @change="handleChange"
                              >
                                <a-select-option
                                  v-for="i in item.options"
                                  :value="i.value">{{ i.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                          </div>
                        </div>
                        <!-- 电网 -->
                        <div v-show="item.key === 2">
                          <div class="line_item">
                            <a-form-item
                              v-for="item in formGrid()"
                              class="line_form_item"
                              :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                              :name="item.name"
                              :rules="item.rules"
                            >
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default"
                                v-model:value="formState[item.name]" size="small"
                                v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                                :formatter="value => `${Decimal(value).mul(Decimal(100))}`"
                                :parser="value => Decimal(value).div(Decimal(100)) "
                                :min="0"
                                :max="1"
                                :step="0.01"
                              />
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]"
                                size="small"
                                v-if="item.type === 'number'&&item.numberType !== 'ratio'" 
                                :min="0"
                              />
                              
                              <a-select
                                v-else-if="item.type === 'select'"
                                ref="select"
                                v-model:value="formState[item.name]"
                                style="width: 120px"
                                @change="handleChange"
                              >
                                <a-select-option
                                  v-for="i in item.options"
                                  :value="i.value">{{ i.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                            <a-form-item
                              class="line_form_item"
                              label="网购电价"
                              name="item.name"
                              rules="item.rules"
                            >
                              <div class="grid_price">
                                <a-cascader
                                  v-model:value="gridPricevalue"
                                  placeholder="Please select"
                                  :options="gridPriceList"
                                  @change="selectGridPrice"
                                 
                                >
                                  <a-button type="primary" size="small">{{ curGridPrice?.[1]?.label ? '重新选择' : '请选择' }}</a-button>
                                </a-cascader>
                                <div v-if="curGridPrice?.[1]" class="desc_wrap">
                                  {{ curGridPrice?.[0]?.label }},
                                  {{ curGridPrice?.[1]?.label }}
                                </div>
                                
                              </div>
                            </a-form-item>
                          </div>
                        
                        </div>
                        <!-- 储能 -->
                        <div v-show="item.key === 3">
                          <div class="line_item">
                            <a-form-item
                              v-for="item in formBat()"
                              class="line_form_item"
                              :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                              :name="item.name"
                              :rules="item.rules"
                            >
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default"
                                v-model:value="formState[item.name]" size="small"
                                v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                                :formatter="value => `${Decimal(value).mul(Decimal(100))}`"
                                :parser="value => Decimal(value).div(Decimal(100)) "
                                :min="0"
                                :max="1"
                                :step="0.01"
                              />
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]"
                                size="small"
                                v-if="item.type === 'number'&&item.numberType !== 'ratio'" 
                                :min="0"
                              />
                              <a-select
                                size="small"
                                v-else-if="item.type === 'select'"
                                ref="select"
                                v-model:value="formState[item.name]"
                                style="width: 100px"
                                @change="handleChange"
                              >
                                <a-select-option
                                  v-for="i in item.options"
                                  :value="i.value">{{ i.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                            <a-form-item
                              class="line_form_item"
                              label="容量范围(MWh)"
                            >
                              <div class="range_item">
                                <a-input-number class="input_deal_wrap2" v-model:value="formState.es_min_capacity" size="small" :min="0" />
                                <div class="middle_line">—</div>
                                <a-input-number class="input_deal_wrap2" v-model:value="formState.es_max_capacity" size="small" :min="0" />
                              </div>
                            </a-form-item>
                          </div>
                          <div class="common_form_item">
                            <div class="i_label" >储能选择</div>
                            <a-table
                              class="i_val"
                              rowKey="id"
                              :row-selection="{ selectedRowKeys: state.selectedBatRowKeys, onChange: onSelectBatChange, type: 'radio' }"
                              :columns="batColumns()"
                              :data-source="deviceList.bat" :pagination="false"
                              size="small"
                            >
                            </a-table>
                          </div>
                        </div>
                        <!-- 制氢 -->
                        <div v-show="item.key === 4">
                          <div class="line_item">
                            <a-form-item
                              v-for="item in formAlk()"
                              class="line_form_item"
                              :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                              :name="item.name"
                              :rules="item.rules"
                            >
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default"
                                v-model:value="formState[item.name]" size="small"
                                v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                                :formatter="value => `${Decimal(value).mul(Decimal(100))}`"
                                :parser="value => Decimal(value).div(Decimal(100)) "
                                :min="0"
                                :max="1"
                                :step="0.01"
                              />
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]"
                                size="small"
                                v-if="item.type === 'number'&&item.numberType !== 'ratio'" 
                                :min="0"
                              />
                              <a-select
                                v-else-if="item.type === 'select'"
                                ref="select"
                                v-model:value="formState[item.name]"
                                style="width: 120px"
                                @change="handleChange"
                              >
                                <a-select-option
                                  v-for="i in item.options"
                                  :value="i.value">{{ i.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                            <a-form-item
                              class="line_form_item"
                              label="容量范围(MW)"
                            >
                              <div class="range_item">
                                <a-input-number class="input_deal_wrap2" v-model:value="formState.ele_min_capacity" size="small" :min="0" />
                                <div class="middle_line">—</div>
                                <a-input-number class="input_deal_wrap2" v-model:value="formState.ele_max_capacity" size="small" :min="0" />
                              </div>
                            </a-form-item>
                          </div>
                          <div class="common_form_item">
                            <div class="i_label" >电解槽选择</div>
                            <a-table
                              class="i_val"
                              rowKey="id"
                              :row-selection="{
                                selectedRowKeys: state.selectedAlkRowKeys, onChange: onSelectALKChange,
                                getCheckboxProps
                              }"
                              :columns="alkColumns()"
                              :data-source="deviceList.alk" :pagination="false"
                              size="small"
                            >
                              <template #bodyCell="{ column, record, text }">
                                <template v-if="column.key === 'ele_type'">
                                  <a-tag :color="getOptionByValue(column.options, text)?.color">{{ getOptionByValue(column.options, text)?.label  }}</a-tag>
                                </template>
                              </template>

                            </a-table>
                          </div>
                        </div>
                        <!-- 储氢-->
                        <div v-show="item.key === 5">
                          <div class="line_item">
                            <a-form-item
                              v-for="item in formAlkStorage()"
                              class="line_form_item"
                              :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                              :name="item.name"
                              :rules="item.rules"
                            >
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default"
                                v-model:value="formState[item.name]" size="small"
                                v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                                :formatter="value => `${Decimal(value).mul(Decimal(100))}`"
                                :parser="value => Decimal(value).div(Decimal(100)) "
                                :min="0"
                                :max="1"
                                :step="0.01"
                              />
                              <a-input-number
                                class="input_deal_wrap" :defaultValue="item.default" v-model:value="formState[item.name]"
                                size="small"
                                v-if="item.type === 'number'&&item.numberType !== 'ratio'" 
                                :min="0"
                              />
                              <a-select
                                v-else-if="item.type === 'select'"
                                ref="select"
                                v-model:value="formState[item.name]"
                                style="width: 120px"
                                @change="handleChange"
                              >
                                <a-select-option
                                  v-for="i in item.options"
                                  :value="i.value">{{ i.label }}
                                </a-select-option>
                              </a-select>
                            </a-form-item>
                          </div>
                          <!-- <div class="k_v_list" >
                            <div class="k_v_item" style="margin: 0 0 10px 0;">
                              <div class="label">供氢曲线</div>
                              <div class="value">
                                <a-button type="primary" size="small" @click="H2ConsumeModalRef.open()">请选择</a-button>
                                <div class="desc_wrap">{{ alkStorageItem.desc }}</div>
                              </div>
                            </div>
                          </div> -->
                          <div class="common_form_item">
                            <div class="i_label" >储氢罐选择</div>
                            <a-table
                              class="i_val"
                              rowKey="id"
                              :row-selection="{ selectedRowKeys: state.selectedAlkStoreRowKeys, onChange: onSelectAlkStoreChange, type: 'radio'  }"
                              :columns="alkStorageColumns()"
                              :data-source="deviceList.alkStorage" :pagination="false"
                              size="small"
                            >
                            </a-table>
                          </div>
                        </div>
                      </a-tab-pane>
                    </a-tabs>
                  </div>
                </div>
              </div>
              <div class="box_wrap">
                <div class="b_title">其他配置</div>
                <div class="b_body">
                  <!-- <a-collapse
                    ghost
                    v-model:activeKey="activeCollapKey"
                    style="background: #fff;">
                    <a-collapse-panel :key="1" header="投资成本">
                      <div class="line_item">
                        <template  v-for="item in investCostData">
                          <a-form-item
                            v-if="item.visible"
                            class="line_form_item"
                            :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                            :name="item.name"
                            :rules="item.rules"
                          >
                            <a-input-number
                              class="input_deal_wrap"
                              :defaultValue="item.default" v-model:value="formState[item.name]"
                              size="small" v-if="item.type === 'number'" 
                              :min="0" />
                          </a-form-item>
                        </template>
                      </div>
                    </a-collapse-panel>
                    <a-collapse-panel :key="2" header="运营成本">
                      <div class="line_item">
                        <template  v-for="item in operationCostData">
                          <a-form-item
                            v-if="item.visible"
                            class="line_form_item"
                            :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                            :name="item.name"
                            :rules="item.rules"
                          >
                            <a-input-number
                              class="input_deal_wrap" :defaultValue="item.default"
                              v-model:value="formState[item.name]" size="small"
                              v-if="item.type === 'number'&&item.numberType === 'ratio'" 
                              :min="0"
                              :max="1"
                              :step="0.01"
                            />
                            <a-input-number
                              class="input_deal_wrap" :defaultValue="item.default"
                              v-model:value="formState[item.name]" size="small"
                              v-if="item.type === 'number'&&item.numberType !== 'ratio'"  :min="0"
                            />
                          </a-form-item>
                        </template>
                      </div>
                    </a-collapse-panel>
                  </a-collapse> -->
                  <div class="line_item">
                    <a-form-item
                      v-for="item in otherCost()"
                      class="line_form_item"
                      :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                      :name="item.name"
                      :rules="item.rules"
                    >
                      <a-input-number
                        class="input_deal_wrap" :defaultValue="item.default"
                        v-model:value="formState[item.name]" size="small"
                        v-if="item.type === 'number'&&item.numberType === 'ratio'"
                        :formatter="value => `${Decimal(value||0).mul(Decimal(100))}`"
                        :parser="value => Decimal(value||0).div(Decimal(100))"
                        :min="0"
                        :max="1"
                        :step="0.01"
                      />
                      <a-input-number
                        class="input_deal_wrap" :defaultValue="item.default"
                        v-model:value="formState[item.name]" size="small"
                        v-if="item.type === 'number'&&item.numberType !== 'ratio'"
                        :min="0"
                      />
                    </a-form-item>
                  </div>
                </div>
              </div>
            </a-form>
          </a-spin>
        </div>
    </div>
    <div class="button_wrap">
      <a-button @click="toRun" type="primary" block :loading="state.running">运行</a-button>
    </div>
    <!-- pv -->
    <TrendChartList
      :chartList="pvForecastData"
      :config="{
        modalTitle: '光伏出力曲线',
        hasZoom: true,
        canUploadSelf: true,
        default: { id: formState.pv_forecast_zone_id }
      }"
      ref="PVPowerModalRef"
      @submit="submitPVPower"
    />
    <TrendChartList
      :chartList="pvDeviceData"
      :selfData="pvSelfData"
      :config="{
        modalTitle: '光伏设备衰减曲线',
        hasZoom: false,
        canUploadSelf: false,
        default: { id: formState?.pv_dev_ids?.[0] }
      }"
      ref="PVDeviceModalRef"
      @submit="submitPVDevice"
    />
    <!-- wind  -->
    <TrendChartList
      :chartList="windForecastData"
      :config="{
        modalTitle: '风机出力曲线',
        hasZoom: true,
        canUploadSelf: true,
        default: { id: formState.wind_forecast_zone_id }
      }"
      ref="WindPowerModalRef"
      @submit="submitWindPower"
    />
    <!-- <TrendChartList
      :chartList="windDeviceData"
      :config="{
        modalTitle: '风机设备衰减曲线',
        hasZoom: false,
        canUploadSelf: false
      }"
      ref="WindDeviceModalRef"
      @submit="submitWindDevice"
    /> -->
    <!-- alk storage  -->
    <TrendChartList
      :chartList="h2ConsumeData"
      :config="{
        modalTitle: '需氢曲线',
        hasZoom: true,
        canUploadSelf: false,
        default: { id: formState.absorb_id }

      }"
      ref="H2ConsumeModalRef"
      @submit="submitH2Consume"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, watch } from 'vue'
import Decimal from "decimal.js" 
import { message } from 'ant-design-vue'
import {
  getProjects, getForecast, getDevice, getH2Consume,
  getGridPrice, submitTask, getSolution
} from '@/api/project'
import { useRouter, useRoute } from 'vue-router'
import {
  formBasicInfo, formGoalList, formAlgList, formAlk,
  alkColumns, formGrid, batColumns, formBat, formAlkStorage, alkStorageColumns,
  investCost, operationCost, formPV, formWind, otherCost
} from './util'
import { formDefaultVal, formTestVal } from './initValue'
import { formatDate, getOptionByValue } from '@/util'
import TrendChartList from '@/components/TrendChartList/index.vue'

const router = useRouter()
const route = useRoute()
const formRef = ref()
// const tableData = ref([])
const loading = ref(false)
const formState = ref({})
const investCostData = ref(investCost())
const operationCostData = ref(operationCost())
const state = reactive({
  selectedAlkRowKeys: [],
  selectedBatRowKeys: [],
  // Check here to configure the default column
  loading: false,
  running: false

});
const deviceList = ref({
  alk: [],
  bat: [],
  alkStorage: []
})
const allTabKeys = ref([
  { title: '光伏', key: 0 },
  { title: '风机', key: 1 },
  { title: '电网', key: 2 },
  { title: '储能', key: 3 },
  { title: '制氢', key: 4 },
  { title: '储氢', key: 5 },
])
const activeCollapKey = ref([1, 2])
const viceTitle = computed(() => {
  const { projectId, solutionId } = route.query
  if (projectId) {
    if (solutionId) {
      return '修改方案'
    } else {
      return '新建方案'
    }
  } else {
    return '创建项目'
  }
})
const gridPricevalue = ref([])
// pv
const PVPowerModalRef = ref()
const PVDeviceModalRef = ref()
const pvForecastData = ref({})
const pvDeviceData = ref({})
const pvPowerItem = ref({})
const pvDeviceItem = ref({})
const pvSelfData = ref()
const submitPVPower = (item) => {
  // console.log('pv power:', item)
  PVPowerModalRef.value.close()
  pvPowerItem.value = item
  formState.value.pv_forecast_zone_id = item.id
  if (item.id == -10) {
    formState.value.pv_forecast = {
      data: item?.series?.[0]?.data?.length ? item.series[0].data : undefined
    }
    formState.value.pv_forecast_zone_id = -1
  } else {
    formState.value.pv_forecast = null
  }
}
const submitPVDevice = (item) => {
  console.log('pv device:', item)
  PVDeviceModalRef.value.close()
  pvDeviceItem.value = item
  formState.value.pv_dev_ids = [item.id]
}
const getForecastPVData = async () => {
  const { code, msg, data } = await getForecast({ type: 1 })
  pvForecastData.value = data.result.map(i => {
    const { zone, forecast } = i
    const { id, region, country, province, city } = zone
    return {
      yData: forecast.data,
      unit: '',
      // desc: [region, country, province, city].filter(ii => !!ii).join('_'),
      desc: [ province, city].filter(ii => !!ii).join('_'),
      id,
    }
  })
  // console.log('t:', pvForecastData.value )
}
const getDevicePVData = async () => {
  const { code, msg, data } = await getDevice({ type: 1 })
  pvDeviceData.value = data.result.map(i => {
    const { baseInfo: { id, manufacturer, model }, params } = i
    return {
      yData: params.damp_curve.data,
      unit: '',
      desc: [manufacturer, model].filter(ii => !!ii).join('_'),
      id,
    }
  })
  console.log('pv forecast:', pvForecastData.value )
}
// wind
const WindPowerModalRef = ref()
const WindDeviceModalRef = ref()
const windForecastData = ref({})
const windDeviceData = ref({})
const windPowerItem = ref({})
const windDeviceItem = ref({})
const submitWindPower = (item) => {
  console.log('wind power:', item)
  WindPowerModalRef.value.close()
  windPowerItem.value = item
  formState.value.wind_forecast_zone_id = item.id
  if (item.id == -10) {
    formState.value.wind_forecast = {
      data: item?.series?.[0]?.data?.length ? item.series[0].data : undefined
    }
    formState.value.wind_forecast_zone_id = -1
  } else {
    formState.value.wind_forecast = null
  }
}
// const submitWindDevice = (item) => {
//   console.log('pv device:', item)
//   WindDeviceModalRef.value.close()
//   windDeviceItem.value = item
//   formState.value.wind_dev_ids = [item.id]
// }
const getForecastWindData = async () => {
  const { code, msg, data } = await getForecast({ type: 2 })
  windForecastData.value = data.result.map(i => {
    const { zone, forecast } = i
    const { id, region, country, province, city } = zone
    return {
      yData: forecast.data,
      unit: '',
      // desc: [region, country, province, city].filter(ii => !!ii).join('_'),
      desc: [province, city].filter(ii => !!ii).join('_'),
      id,
    }
  })
  console.log('wind forecast:', windForecastData.value )
}
// const getDeviceWindData = async () => {
//   const { code, msg, data } = await getDevice({ type: 2 })
//   pvDeviceData.value = data.result.map(i => {
//     const { baseInfo: { id, manufacturer, model }, params } = i
//     return {
//       yData: params.damp_curve,
//       unit: '',
//       desc: [manufacturer, model].filter(ii => !!ii).join('_'),
//       id,
//     }
//   })
//   console.log('wind device:', pvForecastData.value )
// }

// select goal
const selGoal = (index, flag) => {
  if (!flag) {
    selGoalVal.value[index] = !selGoalVal.value[index]
  }
  formState.value.targetExpr = selGoalVal.value.map((i, index) => i ? formState.value[`target${index}`] : 0)

  console.log('targetExpr:', formState.value.targetExpr)
}

// scene
const curTabKeys = ref([])
const activeKey = ref(4);
const selGoalVal = ref([false, false, false, false])
const selSceneVal = ref([false, false, false, false, false, false])

const setCostDataVisible = (index) => {
  investCostData.value.forEach(i => {
    if (i.tag === index) {
      i.visible = !i.visible
    }
  })
  operationCostData.value.forEach(i => {
    if (i.tag === index) {
      i.visible = !i.visible
    }
  })
}
const selScene = (index) => {
  selSceneVal.value[index] = !selSceneVal.value[index]
  formState.value.topology = selSceneVal.value.map((i, index) => i ? 1 : 0)
  curTabKeys.value = allTabKeys.value.filter((i, index) => selSceneVal.value[index])
  if (selSceneVal.value[index]) {
    activeKey.value = index
  } else {
    activeKey.value = formState.value.topology.findIndex(i => !!i)
  }
  // console.log('topo:', formState.value.topology)
  setCostDataVisible(index)
}
// alk

const hasSelectedALK = computed(() => state.selectedAlkRowKeys.length > 0)

const onSelectALKChange = selectedAlkRowKeys => {
  // console.log('selectedAlkRowKeys changed: ', selectedAlkRowKeys);
  state.selectedAlkRowKeys = selectedAlkRowKeys;
  formState.value.ele_dev_ids = selectedAlkRowKeys;
}

const getAlkList = async () => {
  const { code, msg, data } = await getDevice({ type: 4 })
  deviceList.value.alk = data.result.map(i => {
    const { baseInfo, params } = i
    return {
      ...baseInfo,
      ...params,
    }
  })
}

const getCheckboxProps = (record) => {
  // console.log('selectedAlkRowKeys changed: ', state.selectedAlkRowKeys, record);

  // return {
  //   props: {
  //     disabled: state.selectedAlkRowKeys.length >= 4? state.selectedAlkRowKeys.includes(record.id) === false: false,
  //   },
  // }
}


// wind
// grid
const gridPriceList = ref([])
const curGridPrice = ref({})
const getGPrice = async() => {
  const { code, msg, data } = await getGridPrice()
  gridPriceList.value = data.result.map(item => {
    const { price, zone: { city, country, id, province, region } } = item
    return {
      // label: [region, country, province, city].filter(p => !!p).join('_'),
      label: [city].filter(p => !!p).join('_'),
      value: id,
      children: Object.keys(price).map(c => {
        const r = parseInt(c)
        return { label: r, value: r }
      })
    }
  })
  console.log('grid price:', data.result, gridPriceList.value)
}
const selectGridPrice = (item, options) => {
  console.log('slect price:', item, options)
  formState.value.grid_zone_id = item[0]
  formState.value.grid_year = item[1]
  curGridPrice.value = options
}
// bat
const getBatList = async () => {
  const { code, msg, data } = await getDevice({ type: 3 })
  deviceList.value.bat = data.result.map(i => {
    const { baseInfo, params } = i
    return {
      ...baseInfo,
      ...params,
    }
  })
}

const onSelectBatChange = selectedBatRowKeys => {
  console.log('selectedBatRowKeys changed: ', selectedBatRowKeys);
  state.selectedBatRowKeys = selectedBatRowKeys;
  formState.value.es_dev_ids = selectedBatRowKeys;
}


// alk storage
// selectedAlkStoreRowKeys
const h2ConsumeData = ref([])
const H2ConsumeModalRef = ref()
const alkStorageItem = ref({})

const getAlkStorageList = async () => {
  const { code, msg, data } = await getDevice({ type: 5 })
  deviceList.value.alkStorage = data.result.map(i => {
    const { baseInfo, params } = i
    return {
      ...baseInfo,
      ...params,
    }
  })
}

const submitH2Consume = (item) => {
  console.log('alk storage:', item)
  H2ConsumeModalRef.value.close()
  alkStorageItem.value = item
  formState.value.absorb_id = item.id //  hs_dev_ids
}

const onSelectAlkStoreChange = selectedAlkStoreRowKeys => {
  console.log('selectedAlkStoreRowKeys changed: ', selectedAlkStoreRowKeys);
  state.selectedAlkStoreRowKeys = selectedAlkStoreRowKeys;
  formState.value.hs_dev_ids = selectedAlkStoreRowKeys;
}

const getAlkStorageData = async () => {
  const { code, msg, data } = await getH2Consume({ type: 2 })
  h2ConsumeData.value = data.result.map(i => {
    const { id, name, demandCurve } = i
    return {
      yData: demandCurve.data,
      unit: '',
      desc: name,
      id,
    }
  })
  console.log('wind forecast:', windForecastData.value )
}
const checkForm = () => {
  const targetAllCount = formState.value.targetExpr.reduce((a,b) => a + b)
  if(targetAllCount !== 1) {
    message.warn('已选求解目标权重总和需为100% !')
    return false
  }

  const hasPv = formState.value.pv_forecast_zone_id > 0 || formState.value.pv_forecast?.data?.length
  const hasWind = formState.value.wind_forecast_zone_id > 0 || formState.value.wind_forecast?.data?.length
  const hasNewEnergy = hasPv || hasWind
  
  if (!hasNewEnergy) {
    message.warn('光伏和风机出力曲线请至少选择1项!')
    return false
  }
  
  return true
}
const toRun = async () => {
  // const newFormData = new FormData()
  // newFormData.append()
  const values = await formRef.value.validateFields()
  // const formData = new FormData()
  const result = {
    ...values,
    ...formState.value,
    hs_min_capacity: 0, // TODO
    hs_max_capacity: 0, // TODO
  }

  const tmpKey = ['target0', 'target1', 'target2', 'target3', 'scene0', 'scene1', 'scene2', 'scene3', 'scene4', 'scene5']
  tmpKey.forEach(key =>result.hasOwnProperty(key) && delete result[key])

  console.log('re:', result, values, formState.value)
  // return
  const check = checkForm()
  if (!check) {
    return
  }
  // return;
  state.loading = true
  state.running = true
  const { code, msg, data } = await submitTask(result)
  state.running = false
  state.loading = false
  if (code === 0) {
    message.success('已提交运行')
    const query = { taskId:  data.taskId }
    const { projectId, solutionId } = route.query
    if (projectId) {
      query.projectId = projectId
    }
    // TODO
    // if (solutionId) {
    //   query.solutionId = solutionId
    // }
    const link = router.resolve({
      name: 'projectDetail',
      query
    })
    window.open(link.href, '_blank')
  } else {
    message.error(msg)
  }
}

const initFormData = () => {
  formState.value = formDefaultVal
  formState.value.topology.forEach((i, index) => {
    if (i === 0) {
      setCostDataVisible(index)
    }
  })
  // formState.value = formTestVal
}

const initGoalAndSceneSelect = () => {
  const { targetExpr, topology } = formState.value

  const tarList = ['target0', 'target1', 'target2', 'target3']
  const sceneList = ['scene0', 'scene1', 'scene2', 'scene3', 'scene4', 'scene5']
  tarList.forEach((item, index) => {
    formState.value[item] = targetExpr[index]
    selGoalVal.value[index] = !!targetExpr[index]
  })
  sceneList.forEach((item, index) => {
    formState.value[item] = topology[index]
    selSceneVal.value[index] = !!topology[index]
  })
  curTabKeys.value = allTabKeys.value.filter((i, index) => selSceneVal.value[index])
  activeKey.value = allTabKeys.value.findIndex((i, index) => selSceneVal.value[index])
}

// watch route
watch(route, (val) => {
  // console.log('query:', val)
})

const initData = async () => {
  initFormData()

  const { projectId, solutionId } = route.query
  if (projectId || solutionId) {
    state.loading = true
  }
  await getForecastPVData()
  await getDevicePVData()
  await getForecastWindData()
  getGPrice()
  getAlkList()
  getBatList()
  getAlkStorageList()
  getAlkStorageData()
  if (solutionId) {
    state.loading = false
  }

}
const initCalcParams = async () => {
  const { projectId, solutionId } = route.query
  if (projectId == undefined) {
    console.log('Pure create project')
    return
  }
  const query = { }
  let newCalcParams = {}
  query.projectId = parseInt(projectId)

  if (solutionId) {
    query.solutionId = parseInt(solutionId)
  }
  console.log('enter params22:')

  state.loading = true
  const { code, msg, data: { project, solution, calcParams } } = await getSolution(query)
  state.loading = false
  console.log('enter params:', project, solution, calcParams)

  newCalcParams = {
    ...project,
    projectName: project.name,
  }
  if (projectId && solutionId) {
    const { targetExpr, topology } = solution
    newCalcParams = {
      ...newCalcParams,
      ...solution,
      ...calcParams,
      target0: targetExpr[0],
      target1: targetExpr[1],
      target2: targetExpr[2],
      target3: targetExpr[3],
      scene0: topology[0],
      scene1: topology[1],
      scene2: topology[2],
      scene3: topology[3],
      scene4: topology[4],
      scene5: topology[5],
      // pv_forecast: calcParams?.pv_forecast?.data,
      // wind_forecast: calcParams?.wind_forecast?.data
    }
    pvSelfData.value =  calcParams?.pv_forecast?.data

    if (calcParams?.pv_forecast?.data?.length) {
      pvForecastData.value.push({
        yData: calcParams?.pv_forecast?.data,
        desc: `自定义数据-${solution.name}`,
        id: -1
      })  
    }
    if (calcParams?.wind_forecast?.data?.length) {
      windForecastData.value.push({
        yData: calcParams?.wind_forecast?.data,
        desc: `自定义数据-${solution.name}`,
        id: -1
      })  
    }

    pvPowerItem.value = {
      desc: pvForecastData.value.find(i => i.id === calcParams.pv_forecast_zone_id)?.desc
    }
    pvDeviceItem.value = pvDeviceData.value?.find(i => i.id === calcParams?.pv_dev_ids?.[0])
    

    windPowerItem.value = {
      desc: windForecastData.value.find(i => i.id === calcParams.wind_forecast_zone_id)?.desc
    }
    // console.log('rrr:', newCalcParams)
    state.selectedBatRowKeys = calcParams.es_dev_ids
    state.selectedAlkRowKeys = calcParams.ele_dev_ids
    // grid
    gridPricevalue.value = [calcParams.grid_zone_id, calcParams.grid_year]
    const zoneItem = gridPriceList.value?.find(i => i.value === calcParams.grid_zone_id)
    const yearItem = zoneItem?.children?.find(i => i.value === calcParams.grid_year)
    curGridPrice.value  = [zoneItem, yearItem]
    console.log('grid view:',  curGridPrice.value)
    // store alk
    state.selectedAlkStoreRowKeys = calcParams.hs_dev_ids


  }

  if (projectId && !solutionId) {
    newCalcParams = {
      ...formDefaultVal, // TODO
      ...project,
      projectName: project.name
    }
  }
  if (code == 0) {
    formState.value = newCalcParams
  } else {
    message.error(msg)
  }
}
onMounted(async () => {
  // console.log('qq:', route.query)
  await initData()


  await initCalcParams()
  initGoalAndSceneSelect()
})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  font-size: 12px;
  padding: 10px 20px 30px 20px;
  position: relative;
  * {
    // font-size: 12px;
  }
  .button_wrap {
    position: fixed;
    bottom: 5px;
    right: 0;
    left: 0;
    padding: 0 40px;
    z-index: 10;
    // margin-left: 200px;
 
  }
}
.content_wrap {
  margin-top: 20px;
  .box_wrap {
    padding: 20px 20px 15px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .b_title {
      font-size: 14px;
      font-weight: bold;
    }
    .b_title2 {
      font-weight: bold;
    }
    .b_body {
      margin-top: 20px;
    }
    .line_item {
      display: flex;
      flex-wrap: wrap;
      .line_form_item {
        width: 25%;
        margin-bottom: 10px;
        font-size: 12px;
      }
      .desc_wrap {
        font-size: 12px;
        margin: 3px 0 0px 0;
        color: rgba(44, 170, 137, 1)
      }
      @media (max-width: 1300px) {
        .line_form_item {
          width: 33%;
        }
      }
    }
    .g_tips {
      font-size: 12px;
      margin: 10px 0 20px 0;
    }
  }
  .goal_list {
    display: flex;
    align-items: center;
    // margin-bottom: 20px;
    .goal_item_wrap {
      width: 20%;
    }
    .g_title {
      margin-right: 10px;
    }
    .goal_item {
      border: 1px solid #ccc;
      display: flex;
      align-items: center;
      padding: 5px 10px;
      margin-right: 20px;
      &:hover {
        cursor: pointer;
        // color: rgba(44, 170, 137, 1);
        border-color: rgba(44, 170, 137, 1);
      }
      .v_line {
        margin: 0 5px;
      }
    }
    .sel_item {
      .goal_item {
        color: rgba(44, 170, 137, 1);
        background-color: rgba(202, 242, 229, 0.2);
        border-color: rgba(44, 170, 137, 1);
        :deep(.ant-input-number-input) {
          background: rgba(202, 242, 229, 0.2);
          color: rgba(44, 170, 137, 1);

        }
      }
    }
  }
  .scene_title_wrap {
    display: flex;
    justify-content: space-between;
    .s_t_item {
      display: flex;
      align-items: center;
      border: 1px solid #eee;
      width: 19%;
      padding: 10px 8px;
      // margin-right: 1%;
      transition: all 0.2s;
      &:hover {
        cursor: pointer;
        // color: rgba(44, 170, 137, 1);
        border-color: rgba(44, 170, 137, 1);
        box-shadow: 3px 3px 5px #ddd;
        // img {
        //   position: relative;
        //   left: -10000px;
        //   filter: drop-shadow(rgba(44, 170, 137, 1) 10000px 0)
        // }
      }
    }
    .s_t_item_sp {
      display: flex;
      align-items: center;
      border: 1px solid #eee;
      padding: 10px 8px;
      margin-bottom: 5px;
      &:last-child {
        margin-bottom: 0;
      }
      transition: all 0.2s;
      &:hover {
        cursor: pointer;
        // color: rgba(44, 170, 137, 1);
        border-color: rgba(44, 170, 137, 1);
        border-color: rgba(44, 170, 137, 1);
        box-shadow: 3px 3px 5px #ddd;
        // img {
        //   position: relative;
        //   left: -10000px;
        //   filter: drop-shadow(rgba(44, 170, 137, 1) 10000px 0)
        // }
      }
    }
    .s_t_item_sel {
      color: rgba(44, 170, 137, 1);
      cursor: pointer;
      border-color: rgba(44, 170, 137, 1);
      img {
        position: relative;
        left: -10000px;
        filter: drop-shadow(rgba(44, 170, 137, 1) 10000px 0)
      }
    }
    .pv_wind_wrap {
      // margin-right: 1%;
      width: 19%;
    }
    .left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40%;
      height: 100%;
      // margin-right: 20px;
      color: rgba(88, 96, 103, 1);
      // background: url('@/assets/imgs/createProject/grid.svg')  0 50%/contain no-repeat;
    }
    .right {
      .r_title {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
  .scene_content_wrap {
    .range_item {
      display: flex;
      align-items: center;
      .middle_line {
        margin: 0 3px;
        color: #bbb;
      }
    }
    .common_form_item {
      .i_label {
        font-size: 14px;
        margin-right: 10px;
      }
      .i_val {
        flex-grow: 1;
      }
      display: flex;
    }
    .k_v_list {
      display: flex;
      align-items: center;
      .k_v_item {
        display: flex;
        align-items: center;
        width: 25%;
      }
      .label {
        width: 110px;
        flex-shrink: 0;
      }
      .value {
        display: flex;
        align-items: center;
        .desc_wrap {
          font-size: 12px;
          margin: 3px 0 3px 5px;
        }
        // flex-grow: 1;
      }
    }
  }
  .grid_price {
    // display: flex
  }
}

</style>
