<template>
  <div class="sensitivity-analysis">
    <h2 class="section-title">敏感性分析</h2>

    <!-- 有数据时显示分析内容 -->
    <div v-if="sensitivityData && sensitivityData.length > 0" class="analysis-content">
      <!-- 遍历每个指标的敏感性分析 -->
      <div
        v-for="(indicator, index) in sensitivityData"
        :key="indicator.key"
        class="indicator-section"
      >
        <h3 class="indicator-title">{{ getIndicatorName(indicator.key) }}</h3>

        <div class="analysis-table-wrapper">
          <a-table
            :columns="getTableColumns(indicator)"
            :data-source="getTableData(indicator)"
            :pagination="false"
            size="small"
            bordered
            class="sensitivity-table"
          />
        </div>
      </div>
    </div>

    <!-- 无数据时显示空状态 -->
    <div v-else class="empty-state">
      <div class="empty-text">暂无敏感性分析数据</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 接收父组件传递的敏感性分析数据
const props = defineProps({
  sensitivityData: {
    type: Array,
    default: () => []
  }
})

// 指标名称映射
const indicatorNameMap = {
  'investCost': '静态投资成本',
  'gridHydrogenElectricityPriceNoTax': '下网电价(不含税)',
  'gridElectricityPriceNoTax': '上网电价(不含税)',
  'baseLoanRate': '借款利率',
  'financingRatioBase': '贷款比例',
  'hydrogenPriceNoTax': '氢气售价(不含税)'
}

// 获取指标名称
const getIndicatorName = (key) => {
  return indicatorNameMap[key] || key
}

// 获取变化类型描述
const getChangeTypeDesc = (type) => {
  switch (type) {
    case -1:
      return '固定值'
    case 0:
      return '百分比浮动'
    case 1:
      return '加减变化值'
    default:
      return '未知类型'
  }
}

// 格式化变化值显示
const formatChangeValue = (value, type) => {
  if (type === 0) {
    // 百分比浮动
    return `${(value * 100).toFixed(1)}%`
  } else if (type === 1) {
    // 加减变化值
    return value >= 0 ? `+${value}` : `${value}`
  } else {
    // 固定值
    return value.toString()
  }
}

// 格式化结果值显示（百分比）
const formatResultValue = (value) => {
  return `${(value * 100).toFixed(2)}%`
}

// 生成表格列配置
const getTableColumns = (indicator) => {
  console.log('in:', indicator)
  // const unit = indicator.type === 0 ? '%'
  const unitMap = {
    investCost: { unit: '万元' },
    gridElectricityPriceNoTax: { unit: '元/kWh' },
    gridHydrogenElectricityPriceNoTax: { unit: '元/kWh' },
    baseLoanRate: { unit: '%' },
    financingRatioBase: { unit: '%' },
    hydrogenPriceNoTax: { unit: '元/kg' }
  }
  const columns = [
    {
      title: '变化幅度',
      dataIndex: 'changeValue',
      key: 'changeValue',
      width: 120,
      align: 'center'
    },
    {
      title: `值(${unitMap[indicator.key]?.unit || ''})`,
      dataIndex: 'cur_value',
      key: 'cur_value',
      width: 120,
      align: 'center'
    },
    {
      title: '项目投资财务内部收益率(税后)',
      dataIndex: 'projectInvestmentFIRR_afterTax',
      key: 'projectInvestmentFIRR_afterTax',
      width: 200,
      align: 'center'
    },
    {
      title: '资本金财务内部收益率(税后)',
      dataIndex: 'equityCapitalFIRR_afterTax',
      key: 'equityCapitalFIRR_afterTax',
      width: 200,
      align: 'center'
    }
  ]
  
  return columns
}

// 生成表格数据
const getTableData = (indicator) => {
  if (!indicator.value || !indicator.result) {
    return []
  }

  // 确保 value 和 result 数组长度一致
  const minLength = Math.min(indicator.value.length, indicator.result.length)

  return indicator.value.slice(0, minLength).map((changeValue, index) => {
    const result = indicator.result[index] || {}

    return {
      key: index,
      changeValue: formatChangeValue(changeValue, indicator.type),
      cur_value: result.cur_value || '-',
      projectInvestmentFIRR_afterTax: formatResultValue(result.projectInvestmentFIRR_afterTax || 0),
      equityCapitalFIRR_afterTax: formatResultValue(result.equityCapitalFIRR_afterTax || 0)
    }
  })
}
</script>

<style scoped lang="less">
@import '@/style/base.less';

.sensitivity-analysis {
  background: white;
  padding: 16px;
  border-radius: 4px;
  margin-top: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #333;
}

.analysis-content {
  .indicator-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .indicator-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #555;
    padding-left: 8px;
    border-left: 3px solid #1890ff;
  }

  .analysis-table-wrapper {
    .sensitivity-table {
      font-size: 12px;

      :deep(.ant-table-thead > tr > th) {
        background: #fafafa;
        font-weight: 500;
        color: #333;
        padding: 8px;
        font-size: 12px;
        text-align: center;
      }

      :deep(.ant-table-tbody > tr > td) {
        padding: 8px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 12px;
        text-align: center;
      }

      :deep(.ant-table-tbody > tr:hover > td) {
        background: #f5f5f5;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;

  .empty-text {
    font-size: 14px;
  }
}
</style>
