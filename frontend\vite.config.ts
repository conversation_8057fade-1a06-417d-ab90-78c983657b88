import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueJsx from '@vitejs/plugin-vue-jsx'
import VueDevTools from 'vite-plugin-vue-devtools'
import { expressPlugin } from './mockServer'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // vueJsx(),
    // VueDevTools(),
    // expressPlugin()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      "/api/v1": {
        target: "http://***********:8099", // pro:***********:9099  // test: ***********:8099
        changeOrigin: true,
        ws: true
      },
    },

  },
  build: {
    outDir: 'dist_dev_cap_v2_test' // prod: dist_dev_cap_v2, test: dist_dev_cap_v2_test，zh: dist_dev_cap_v2_test_zh
  }
})
